# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@0no-co/graphql.web@npm:^1.0.5, @0no-co/graphql.web@npm:^1.0.8":
  version: 1.1.2
  resolution: "@0no-co/graphql.web@npm:1.1.2"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
  peerDependenciesMeta:
    graphql:
      optional: true
  checksum: 10c0/7074de29681f0563cb9a90d702c7cda4443dce858e09f9a09adbafe32c302890cab81959ccba4ed7ac3e332423b2928a1dc95dd4a5004e6a5c156b733caa349a
  languageName: node
  linkType: hard

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: 10c0/7b878c48b9d25277d0e1a9b8b2f2312a314af806b4129dc902f2bc29ab09b58236e53964689feec187b28c80d2203aff03829754773a707a8a5987f1b7682d92
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.10.4, @babel/code-frame@npm:~7.10.4":
  version: 7.10.4
  resolution: "@babel/code-frame@npm:7.10.4"
  dependencies:
    "@babel/highlight": "npm:^7.10.4"
  checksum: 10c0/69e0f52986a1f40231d891224f420436629b6678711b68c088e97b7bdba1607aeb5eb9cfb070275c433f0bf43c37c134845db80d1cdbf5ac88a69b0bdcce9402
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.24.7, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.20.0, @babel/core@npm:^7.25.2":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/core@npm:^7.23.9, @babel/core@npm:^7.27.4":
  version: 7.28.0
  resolution: "@babel/core@npm:7.28.0"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.0"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.6"
    "@babel/parser": "npm:^7.28.0"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.28.0"
    "@babel/types": "npm:^7.28.0"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/423302e7c721e73b1c096217880272e02020dfb697a55ccca60ad01bba90037015f84d0c20c6ce297cf33a19bb704bc5c2b3d3095f5284dfa592bd1de0b9e8c3
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.5, @babel/generator@npm:^7.25.0, @babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.5, @babel/generator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": "npm:^7.28.0"
    "@babel/types": "npm:^7.28.0"
    "@jridgewell/gen-mapping": "npm:^0.3.12"
    "@jridgewell/trace-mapping": "npm:^0.3.28"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/1b3d122268ea3df50fde707ad864d9a55c72621357d5cebb972db3dd76859c45810c56e16ad23123f18f80cc2692f5a015d2858361300f0f224a05dc43d36a92
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/94996ce0a05b7229f956033e6dcd69393db2b0886d0db6aff41e704390402b8cdcca11f61449cb4f86cfd9e61b5ad3a73e4fa661eeed7846b125bd1c33dbc633
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: 10c0/5a0cd0c0e8c764b5f27f2095e4243e8af6fa145daea2b41b53c0c1414fe6ff139e3640f4e2207ae2b3d2153a1abd346f901c26c290ee7cb3881dd922d4ee9232
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.15, @babel/helper-module-imports@npm:^7.25.9, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9, @babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4, @babel/helpers@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4":
  version: 7.25.9
  resolution: "@babel/highlight@npm:7.25.9"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/ae0ed93c151b85a07df42936117fa593ce91563a22dfc8944a90ae7088c9679645c33e00dcd20b081c1979665d65f986241172dae1fc9e5922692fc3ff685a49
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.25.3, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.9, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": "npm:^7.28.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/c2ef81d598990fa949d1d388429df327420357cb5200271d0d0a2784f1e6d54afc8301eb8bdf96d8f6c77781e402da93c7dc07980fcc136ac5b9d5f1fce701b5
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.12.9":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-decorators": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/3af0db6b2468907bcaf62246b2cfd3616ba9239ea1cd26036ec6baff1bc095fe4964853b1d29a79944d36e6e3d331cd130d05b0c41c835266daf7bb9d8e8f87c
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6e0756e0692245854028caea113dad2dc11fcdd479891a59d9a614a099e7e321f2bd25a1e3dd6f3b36ba9506a76f072f63adbf676e5ed51e7eeac277612e3db2
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/686891b81af2bc74c39013655da368a480f17dd237bf9fbc32048e5865cb706d5a8f65438030da535b332b1d6b22feba336da8fa931f663b6b34e13147d12dde
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/95168fa186416195280b1264fb18afcdcdcea780b3515537b766cb90de6ce042d42dd6a204a39002f794ae5845b02afb0fd4861a3308a861204a55e68310a120
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4464bf9115f4a2d02ce1454411baf9cfb665af1da53709c5c56953e5e2913745b0fcce82982a00463d6facbdd93445c691024e310b91431a1e2f024b158f6371
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46ef933bae10b02a8f8603b2f424ecbe23e134a133205bee7c0902dae3021c183a683964cab41ea5433820aa05be0f6f36243551f68a1d94e02ac082cec87aa1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9c50927bf71adf63f60c75370e2335879402648f468d0172bc912e303c6a3876927d8eb35807331b57f415392732ed05ab9b42c68ac30a936813ab549e0246c5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9aa62f5916950f3e5f91657895f4635b1c77e108e453ef12c30dc7670c3441bdd65cd28be20d6ddc9003ed471cc98465785a14cd76c61f077c1c84264f1f28ca
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.12.1, @babel/plugin-syntax-flow@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4d34ca47044398665cbe0293baea7be230ca4090bc7981ffba5273402a215c95976c6f811c7b32f10b326cc6aab6886f26c29630c429aa45c3f350c5ccdfdbbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e66f7a761b8360419bbb93ab67d87c8a97465ef4637a985ff682ce7ba6918b34b29d81190204cf908d0933058ee7b42737423cd8a999546c21b3aabad4affa9a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/69822772561706c87f0a65bc92d0772cea74d6bc0911537904a676d5ff496a6d3ac4e05a166d8125fce4a16605bace141afc3611074e170a994e66e5397787f3
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/14bf6e65d5bc1231ffa9def5f0ef30b19b51c218fcecaa78cd1bdf7939dfdf23f90336080b7f5196916368e399934ce5d581492d8292b46a2fb569d8b2da106f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.0.0-0, @babel/plugin-transform-arrow-functions@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/772e449c69ee42a466443acefb07083bd89efb1a1d95679a4dc99ea3be9d8a3c43a2b74d2da95d7c818e9dd9e0b72bfa7c03217a1feaf108f21b7e542f0943c0
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.25.0":
  version: 7.27.5
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5c1a61f312f18d3807c4df25868161301a7bd0807092b86951fa6b9918e07ee382d58d61a204c3f9ad0b72b8f6f1d18586f8e485c355a3e959c26a070397e95e
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.0.0-0, @babel/plugin-transform-class-properties@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cc0662633c0fe6df95819fef223506ddf26c369c8d64ab21a728d9007ec866bf9436a253909819216c24a82186b6ccbc1ec94d7aaf3f82df227c7c02fa6a704b
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.0.0-0, @babel/plugin-transform-classes@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1071f4cb1ed5deb5e6f8d0442f2293a540cac5caa5ab3c25ad0571aadcbf961f61e26d367a67894976165a543e02f3a19e40b63b909afbed6e710801a590635c
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.24.8, @babel/plugin-transform-destructuring@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f8ac96deef6f9a4cb1dff148dfa2a43116ca1c48434bba433964498c4ef5cef5557693b47463e64a71ffaaf10191c7fab0270844e8dbdc47dc4d120435025df5
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.25.9":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d7165cad11f571a54c8d9263d6c6bf2b817aff4874f747cb51e6e49efb32f2c9b37a6850cdb5e3b81e0b638141bb77dc782a6ec1a94128859fbdf7767581e07c
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.25.2":
  version: 7.27.1
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-flow": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c61c43244aacdcd479ad9ba618e1c095a5db7e4eadc3d19249602febc4e97153230273c014933f5fe4e92062fa56dab9bed4bc430197d5b2ffeb2158a4bf6786
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4635763173a23aae24480681f2b0996b4f54a0cb2368880301a1801638242e263132d1e8adbe112ab272913d1d900ee0d6f7dea79443aef9d3325168cd88b3fb
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.25.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.25.2":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b0abc7c0d09d562bf555c646dce63a30288e5db46fd2ce809a61d064415da6efc3b2b3c59b8e4fe98accd072c89a2f7c3765b400e4bf488651735d314d9feeb
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.24.8, @babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.0.0-0, @babel/plugin-transform-nullish-coalescing-operator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a435fc03aaa65c6ef8e99b2d61af0994eb5cdd4a28562d78c3b0b0228ca7e501aa255e1dff091a6996d7d3ea808eb5a65fd50ecd28dfb10687a8a1095dcadc7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b72cbebbfe46fcf319504edc1cf59f3f41c992dd6840db766367f6a1d232cd2c52143c5eaf57e0316710bee251cae94be97c6d646b5022fcd9274ccb131b470c
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.24.7":
  version: 7.27.3
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.27.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.3"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f2d04f59f773a9480bbaabd082fecdb5fb2b6ae5e77147ae8df34a8b773b6148d0c4260d2beaa4755eb5f548a105f2069124b9cea96f9387128656cbb0730ee4
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/807a4330f1fac08e2682d57bc82e714868fc651c8876f9a8b3a3fd8f53c129e87371f8243e712ac7dae11e090b737a2219a02fe1b6459a29e664fa073c3277bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.0.0-0, @babel/plugin-transform-optional-chaining@npm:^7.24.8":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b18ff5124e503f0a25d6b195be7351a028b3992d6f2a91fb4037e2a2c386400d66bc1df8f6df0a94c708524f318729e81a95c41906e5a7919a06a43e573a525
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.24.7, @babel/plugin-transform-parameters@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/453a9618735eeff5551d4c7f02c250606586fe1dd210ec9f69a4f15629ace180cd944339ebff2b0f11e1a40567d83a229ba1c567620e70b2ebedea576e12196a
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/232bedfe9d28df215fb03cc7623bdde468b1246bdd6dc24465ff4bf9cc5f5a256ae33daea1fafa6cc59705e4d29da9024bb79baccaa5cd92811ac5db9b9244f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a8c4536273ca716dcc98e74ea25ca76431528554922f184392be3ddaf1761d4aa0e06f1311577755bd1613f7054fb51d29de2ada1130f743d329170a1aa1fe56
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.24.7, @babel/plugin-transform-react-display-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-display-name@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6cd474b5fb30a2255027d8fc19975aee1c1da54dd8bc8b79802676096182ca4136302ce65a24fbb277f8fe30f266006bbf327ef6be2846d3681eb57509744125
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/eb8c4b6a79dc5c49b41e928e2037e1ee0bbfa722e4fd74c0b7c0d11103c82c2c25c434000e1b051d534c7261ab5c92b6d1e85313bf1b26e37db3f051ae217b58
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.25.2, @babel/plugin-transform-react-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1a08637c39fc78c9760dd4a3ed363fdbc762994bf83ed7872ad5bda0232fcd0fc557332f2ce36b522c0226dfd9cc8faac6b88eddda535f24825198a689e571af
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/34bc090f4a7e460d82a851971b4d0f32e4bb519bafb927154f4174506283fe02b0f471fc20655c6050a8bf7b748bfa31c7e8f7d688849476d8266623554fbb28
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.24.7":
  version: 7.27.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4ace8ced76b421cd44dd9fa08bebc2f3fd76ec84e532cd1027738f411afdbc239789edd6c96dd1db412fc4a42cead5c1ac98a8aef94f35102f5de1049e64c07a
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.24.7":
  version: 7.27.4
  resolution: "@babel/plugin-transform-runtime@npm:7.27.4"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7d4683410b8d04483e666baf150faeefa6525acf9c53c8631d9bb7c49271fabe4817dad6284a7a8c54c92ce1f24a69cd62f56782bb9bd35135c9933b1c5362ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.0.0-0, @babel/plugin-transform-shorthand-properties@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.0.0-0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c90f403e42ef062b60654d1c122c70f3ec6f00c2f304b0931ebe6d0b432498ef8a5ef9266ddf00debc535f8390842207e44d3900eff1d2bab0cc1a700f03e083
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.25.2, @babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/48f1db5de17a0f9fc365ff4fb046010aedc7aad813a7aa42fb73fcdab6442f9e700dde2cc0481086e01b0dae662ae4d3e965a52cde154f0f146d243a8ac68e93
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.0.0-0, @babel/plugin-transform-unicode-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.22.15":
  version: 7.27.1
  resolution: "@babel/preset-react@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-transform-react-display-name": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx-development": "npm:^7.27.1"
    "@babel/plugin-transform-react-pure-annotations": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a80b02ef08b026cb9830d6512d08c7cd378eef4c0631dacba4aa1106240d9bb76af6373463f0255f4bbdbfcce40375a61e92735375906ba5871629b0c314bc45
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.16.7, @babel/preset-typescript@npm:^7.23.0":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cba6ca793d915f8aff9fe2f13b0dfbf5fd3f2e9a17f17478ec9878e9af0d206dcfe93154b9fd353727f16c1dca7c7a3ceb4943f8d28b216235f106bc0fbbcaa3
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.18.6, @babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.25.0, @babel/runtime@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.0, @babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2, @babel/template@npm:^7.3.3":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse--for-generate-function-map@npm:@babel/traverse@^7.25.3, @babel/traverse@npm:^7.23.0, @babel/traverse@npm:^7.25.3, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.0"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/parser": "npm:^7.28.0"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.0"
    debug: "npm:^4.3.1"
  checksum: 10c0/32794402457827ac558173bcebdcc0e3a18fa339b7c41ca35621f9f645f044534d91bb923ff385f5f960f2e495f56ce18d6c7b0d064d2f0ccb55b285fa6bc7b9
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.23.0, @babel/types@npm:^7.25.2, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.3.3":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/39d556be114f2a6d874ea25ad39826a9e3a0e98de0233ae6d932f6d09a4b222923a90a7274c635ed61f1ba49bbd345329226678800900ad1c8d11afabd573aaf
  languageName: node
  linkType: hard

"@babel/types@npm:^7.28.0":
  version: 7.28.1
  resolution: "@babel/types@npm:7.28.1"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/5e99b346c11ee42ffb0cadc28159fe0b184d865a2cc1593df79b199772a534f6453969b4942aa5e4a55a3081863096e1cc3fc1c724d826926dc787cf229b845d
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 10c0/6b80ae4cb3db53f486da2dc63b6e190a74c8c3cca16bb2733f234a0b6a9382b09b146488ae08e2b22cf00f6c83e20f3e040a2f7894f05c045c946d6a090b1d52
  languageName: node
  linkType: hard

"@callstack/react-theme-provider@npm:^3.0.9":
  version: 3.0.9
  resolution: "@callstack/react-theme-provider@npm:3.0.9"
  dependencies:
    deepmerge: "npm:^3.2.0"
    hoist-non-react-statics: "npm:^3.3.0"
  peerDependencies:
    react: ">=16.3.0"
  checksum: 10c0/ac3463383c35e1a4ef813e869532bf86d3e83a4c211b00616c832a5f08545f04f07f769726664b5010124575af2cf8152aabfb7d4c37a64b22ae8a7f9490df0e
  languageName: node
  linkType: hard

"@egjs/hammerjs@npm:^2.0.17":
  version: 2.0.17
  resolution: "@egjs/hammerjs@npm:2.0.17"
  dependencies:
    "@types/hammerjs": "npm:^2.0.36"
  checksum: 10c0/dbedc15a0e633f887c08394bd636faf6a3abd05726dc0909a0e01209d5860a752d9eca5e512da623aecfabe665f49f1d035de3103eb2f9022c5cea692f9cc9be
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/core@npm:1.4.3"
  dependencies:
    "@emnapi/wasi-threads": "npm:1.0.2"
    tslib: "npm:^2.4.0"
  checksum: 10c0/e30101d16d37ef3283538a35cad60e22095aff2403fb9226a35330b932eb6740b81364d525537a94eb4fb51355e48ae9b10d779c0dd1cdcd55d71461fe4b45c7
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/runtime@npm:1.4.3"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/3b7ab72d21cb4e034f07df80165265f85f445ef3f581d1bc87b67e5239428baa00200b68a7d5e37a0425c3a78320b541b07f76c5530f6f6f95336a6294ebf30b
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.0.2":
  version: 1.0.2
  resolution: "@emnapi/wasi-threads@npm:1.0.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/f0621b1fc715221bd2d8332c0ca922617bcd77cdb3050eae50a124eb8923c54fa425d23982dc8f29d505c8798a62d1049bace8b0686098ff9dd82270e06d772e
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/c0f4f2bd73b7b7a9de74b716a664873d08ab71ab439e51befe77d61915af41a81ecec93b408778b3a7856185244c34c2c8ee28912072ec14def84ba2dec70adf
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.0
  resolution: "@eslint/config-array@npm:0.20.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/94bc5d0abb96dc5295ff559925242ff75a54eacfb3576677e95917e42f7175e1c4b87bf039aa2a872f949b4852ad9724bf2f7529aaea6b98f28bb3fca7f1d659
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.1":
  version: 0.2.2
  resolution: "@eslint/config-helpers@npm:0.2.2"
  checksum: 10c0/98f7cefe484bb754674585d9e73cf1414a3ab4fd0783c385465288d13eb1a8d8e7d7b0611259fc52b76b396c11a13517be5036d1f48eeb877f6f0a6b9c4f03ad
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/259f279445834ba2d2cbcc18e9d43202a4011fde22f29d5fb802181d66e0f6f0bd1f6b4b4b46663451f545d35134498231bd5e656e18d9034a457824b92b7741
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.28.0":
  version: 9.28.0
  resolution: "@eslint/js@npm:9.28.0"
  checksum: 10c0/5a6759542490dd9f778993edfbc8d2f55168fd0f7336ceed20fe3870c65499d72fc0bca8d1ae00ea246b0923ea4cba2e0758a8a5507a3506ddcf41c92282abb8
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.1
  resolution: "@eslint/plugin-kit@npm:0.3.1"
  dependencies:
    "@eslint/core": "npm:^0.14.0"
    levn: "npm:^0.4.1"
  checksum: 10c0/a75f0b5d38430318a551b83e27bee570747eb50beeb76b03f64b0e78c2c27ef3d284cfda3443134df028db3251719bc0850c105f778122f6ad762d5270ec8063
  languageName: node
  linkType: hard

"@expo-google-fonts/inter@npm:^0.4.1":
  version: 0.4.1
  resolution: "@expo-google-fonts/inter@npm:0.4.1"
  checksum: 10c0/982e26d2daae56b4e7740e9c9efab5dd42e35da1464cf7deeb163f09d729d29329f03ba7deb1523a8051e62bfbb6b37334a180832f0aa384d1c59b0cadb74c13
  languageName: node
  linkType: hard

"@expo/cli@npm:0.24.20":
  version: 0.24.20
  resolution: "@expo/cli@npm:0.24.20"
  dependencies:
    "@0no-co/graphql.web": "npm:^1.0.8"
    "@babel/runtime": "npm:^7.20.0"
    "@expo/code-signing-certificates": "npm:^0.0.5"
    "@expo/config": "npm:~11.0.13"
    "@expo/config-plugins": "npm:~10.1.2"
    "@expo/devcert": "npm:^1.1.2"
    "@expo/env": "npm:~1.0.7"
    "@expo/image-utils": "npm:^0.7.6"
    "@expo/json-file": "npm:^9.1.5"
    "@expo/metro-config": "npm:~0.20.17"
    "@expo/osascript": "npm:^2.2.5"
    "@expo/package-manager": "npm:^1.8.6"
    "@expo/plist": "npm:^0.3.5"
    "@expo/prebuild-config": "npm:^9.0.11"
    "@expo/spawn-async": "npm:^1.7.2"
    "@expo/ws-tunnel": "npm:^1.0.1"
    "@expo/xcpretty": "npm:^4.3.0"
    "@react-native/dev-middleware": "npm:0.79.5"
    "@urql/core": "npm:^5.0.6"
    "@urql/exchange-retry": "npm:^1.3.0"
    accepts: "npm:^1.3.8"
    arg: "npm:^5.0.2"
    better-opn: "npm:~3.0.2"
    bplist-creator: "npm:0.1.0"
    bplist-parser: "npm:^0.3.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.3.0"
    compression: "npm:^1.7.4"
    connect: "npm:^3.7.0"
    debug: "npm:^4.3.4"
    env-editor: "npm:^0.4.1"
    freeport-async: "npm:^2.0.0"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    lan-network: "npm:^0.1.6"
    minimatch: "npm:^9.0.0"
    node-forge: "npm:^1.3.1"
    npm-package-arg: "npm:^11.0.0"
    ora: "npm:^3.4.0"
    picomatch: "npm:^3.0.1"
    pretty-bytes: "npm:^5.6.0"
    pretty-format: "npm:^29.7.0"
    progress: "npm:^2.0.3"
    prompts: "npm:^2.3.2"
    qrcode-terminal: "npm:0.11.0"
    require-from-string: "npm:^2.0.2"
    requireg: "npm:^0.2.2"
    resolve: "npm:^1.22.2"
    resolve-from: "npm:^5.0.0"
    resolve.exports: "npm:^2.0.3"
    semver: "npm:^7.6.0"
    send: "npm:^0.19.0"
    slugify: "npm:^1.3.4"
    source-map-support: "npm:~0.5.21"
    stacktrace-parser: "npm:^0.1.10"
    structured-headers: "npm:^0.4.1"
    tar: "npm:^7.4.3"
    terminal-link: "npm:^2.1.1"
    undici: "npm:^6.18.2"
    wrap-ansi: "npm:^7.0.0"
    ws: "npm:^8.12.1"
  bin:
    expo-internal: build/bin/cli
  checksum: 10c0/631fe39fc120f8377f22274c53579a50e37a8f0a63b3e58a60c225335ef3686747e2146a80923004e73d42b2ad12ebdd68468b971852baa9779bac5ce35cb859
  languageName: node
  linkType: hard

"@expo/code-signing-certificates@npm:^0.0.5":
  version: 0.0.5
  resolution: "@expo/code-signing-certificates@npm:0.0.5"
  dependencies:
    node-forge: "npm:^1.2.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/98c908c54f92d6782ae01fef47dd858140dc6013e5376ee3faf9b243327f2b16279441fec171cbde45d0e3ebd0bf72db57b4d4c2a0c4f952285b0b377b2b356b
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:~10.1.1, @expo/config-plugins@npm:~10.1.2":
  version: 10.1.2
  resolution: "@expo/config-plugins@npm:10.1.2"
  dependencies:
    "@expo/config-types": "npm:^53.0.5"
    "@expo/json-file": "npm:~9.1.5"
    "@expo/plist": "npm:^0.3.5"
    "@expo/sdk-runtime-versions": "npm:^1.0.0"
    chalk: "npm:^4.1.2"
    debug: "npm:^4.3.5"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.5.4"
    slash: "npm:^3.0.0"
    slugify: "npm:^1.6.6"
    xcode: "npm:^3.0.1"
    xml2js: "npm:0.6.0"
  checksum: 10c0/d5ef0f002db40cb182058b2fe9df6f5f77ff09e18aa0bc8109047d75cd912487bace59bcff7104c6f68f6b49f89d0b387ab6f90f8069c63c9f3fccb9fb9b99de
  languageName: node
  linkType: hard

"@expo/config-types@npm:^53.0.5":
  version: 53.0.5
  resolution: "@expo/config-types@npm:53.0.5"
  checksum: 10c0/a7c96f65327de5608aedaf0669bc95b721323113064bdad3473d6faa07b619100ef1df5811f3fdb5dc50d05610842aec8d6bc1902dd0345d51ba2d520884487d
  languageName: node
  linkType: hard

"@expo/config@npm:~11.0.12":
  version: 11.0.12
  resolution: "@expo/config@npm:11.0.12"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    "@expo/config-plugins": "npm:~10.1.1"
    "@expo/config-types": "npm:^53.0.5"
    "@expo/json-file": "npm:^9.1.5"
    deepmerge: "npm:^4.3.1"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
    resolve-workspace-root: "npm:^2.0.0"
    semver: "npm:^7.6.0"
    slugify: "npm:^1.3.4"
    sucrase: "npm:3.35.0"
  checksum: 10c0/e570472da465633cc1f72cc03dfab44534df39530f412f3c1e12f00667e3c509dd4253e4c5101f80b7d1bc5702b86fc20f8b5a7220f550c4617ae31a91d6bbf8
  languageName: node
  linkType: hard

"@expo/config@npm:~11.0.13":
  version: 11.0.13
  resolution: "@expo/config@npm:11.0.13"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    "@expo/config-plugins": "npm:~10.1.2"
    "@expo/config-types": "npm:^53.0.5"
    "@expo/json-file": "npm:^9.1.5"
    deepmerge: "npm:^4.3.1"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
    resolve-workspace-root: "npm:^2.0.0"
    semver: "npm:^7.6.0"
    slugify: "npm:^1.3.4"
    sucrase: "npm:3.35.0"
  checksum: 10c0/19cdbc4baa498ca9e55416fd1b2a202cca061e34984236b1f032f3d28cf72a4ddc824bc0cbe3d39c5b5f1117ef65be84c4b05bf62b6fa41d5d049b75af59a17c
  languageName: node
  linkType: hard

"@expo/devcert@npm:^1.1.2":
  version: 1.2.0
  resolution: "@expo/devcert@npm:1.2.0"
  dependencies:
    "@expo/sudo-prompt": "npm:^9.3.1"
    debug: "npm:^3.1.0"
    glob: "npm:^10.4.2"
  checksum: 10c0/3d6a1ce44918c2e5be3bb89d25cfc80551623e4fe5004d4eb29d1edc8edd676258345e64d2aefe56188bc5d4b33e2b7e733a108b2be225af1f90ca86d7170069
  languageName: node
  linkType: hard

"@expo/env@npm:~1.0.7":
  version: 1.0.7
  resolution: "@expo/env@npm:1.0.7"
  dependencies:
    chalk: "npm:^4.0.0"
    debug: "npm:^4.3.4"
    dotenv: "npm:~16.4.5"
    dotenv-expand: "npm:~11.0.6"
    getenv: "npm:^2.0.0"
  checksum: 10c0/a2634073424cc7610fdf507d793adfbc744cc47c1ecc2ba9ba2db68ffad4ead436ce9255699e19b8ade6344eba833fd5dcac95c738c05b5170814c5d90522ade
  languageName: node
  linkType: hard

"@expo/fingerprint@npm:0.13.4":
  version: 0.13.4
  resolution: "@expo/fingerprint@npm:0.13.4"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    arg: "npm:^5.0.2"
    chalk: "npm:^4.1.2"
    debug: "npm:^4.3.4"
    find-up: "npm:^5.0.0"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    ignore: "npm:^5.3.1"
    minimatch: "npm:^9.0.0"
    p-limit: "npm:^3.1.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
  bin:
    fingerprint: bin/cli.js
  checksum: 10c0/a04f81e9862e18654153b4086a0b031bce493b1310560f01f7d5d89101277208865e602223c3be5a47618dfaa5853c4c5cc6598e990374cea7bc82b169ce2582
  languageName: node
  linkType: hard

"@expo/image-utils@npm:^0.7.6":
  version: 0.7.6
  resolution: "@expo/image-utils@npm:0.7.6"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    getenv: "npm:^2.0.0"
    jimp-compact: "npm:0.16.1"
    parse-png: "npm:^2.1.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    temp-dir: "npm:~2.0.0"
    unique-string: "npm:~2.0.0"
  checksum: 10c0/45a8ce5237fecd63a4020295b584d237a024d917ca1bf1e31568bb96703b5fd9351a6912a7ecc0fc5c38d97029f31f86e312c4646beb0207ce578289a3e2bc2d
  languageName: node
  linkType: hard

"@expo/json-file@npm:^9.1.5, @expo/json-file@npm:~9.1.5":
  version: 9.1.5
  resolution: "@expo/json-file@npm:9.1.5"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    json5: "npm:^2.2.3"
  checksum: 10c0/989e3aa6d3e31a7f499d7979c6062694f2bc1fe1a4bc81b64aff74c39f27ed5f52098861897236cdc26b86186062560f3191814a2e8ff5b821a74a71d617f135
  languageName: node
  linkType: hard

"@expo/metro-config@npm:0.20.17, @expo/metro-config@npm:~0.20.17":
  version: 0.20.17
  resolution: "@expo/metro-config@npm:0.20.17"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.5"
    "@babel/parser": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    "@expo/config": "npm:~11.0.12"
    "@expo/env": "npm:~1.0.7"
    "@expo/json-file": "npm:~9.1.5"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.1.0"
    debug: "npm:^4.3.2"
    dotenv: "npm:~16.4.5"
    dotenv-expand: "npm:~11.0.6"
    getenv: "npm:^2.0.0"
    glob: "npm:^10.4.2"
    jsc-safe-url: "npm:^0.2.4"
    lightningcss: "npm:~1.27.0"
    minimatch: "npm:^9.0.0"
    postcss: "npm:~8.4.32"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/d3bb092635e2311a4be162966d87da484fa3543a4ac2640855ede2a44266e563411b352db8e95c1f5ce29387f24c85e2606bbda23bb932d00d3010d256446d0c
  languageName: node
  linkType: hard

"@expo/metro-runtime@npm:5.0.4":
  version: 5.0.4
  resolution: "@expo/metro-runtime@npm:5.0.4"
  peerDependencies:
    react-native: "*"
  checksum: 10c0/3522e7e95c13679a4f150d7cce78253e928adea5593c77af6cda0b56c34f2cb4d4a6d057a8bd455b3e3f63439e321c6b80cd1a7bdfdfa04b2ab1fcc034b3736f
  languageName: node
  linkType: hard

"@expo/osascript@npm:^2.2.5":
  version: 2.2.5
  resolution: "@expo/osascript@npm:2.2.5"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    exec-async: "npm:^2.2.0"
  checksum: 10c0/1fd6d0ebb43eb09d57db02b4053dd4b1aa5ba341aebbe10c1b39afa03c4724c77778e2e5ed932fe2ff24724ff73f464789778ca9e964aea6dfe3481f3ffbf570
  languageName: node
  linkType: hard

"@expo/package-manager@npm:^1.8.6":
  version: 1.8.6
  resolution: "@expo/package-manager@npm:1.8.6"
  dependencies:
    "@expo/json-file": "npm:^9.1.5"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    npm-package-arg: "npm:^11.0.0"
    ora: "npm:^3.4.0"
    resolve-workspace-root: "npm:^2.0.0"
  checksum: 10c0/2d2860016ce15a0c7c6d99bd3df28b0250ce277eae0120696c84ee3edae644524baa43be0874fe7a4796c39ff7ba9de23337360ab2e5fc1501e5da411345143f
  languageName: node
  linkType: hard

"@expo/plist@npm:^0.3.5":
  version: 0.3.5
  resolution: "@expo/plist@npm:0.3.5"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.2.3"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/d0cde0024b6363f3c96ac186a59795d7c7655986407623324083261ea7e8dcaa7014f385baa1a70422765299eb6d828515ebf0d40590caf34f81997288b74cc1
  languageName: node
  linkType: hard

"@expo/prebuild-config@npm:^9.0.10":
  version: 9.0.10
  resolution: "@expo/prebuild-config@npm:9.0.10"
  dependencies:
    "@expo/config": "npm:~11.0.12"
    "@expo/config-plugins": "npm:~10.1.1"
    "@expo/config-types": "npm:^53.0.5"
    "@expo/image-utils": "npm:^0.7.6"
    "@expo/json-file": "npm:^9.1.5"
    "@react-native/normalize-colors": "npm:0.79.5"
    debug: "npm:^4.3.1"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    xml2js: "npm:0.6.0"
  checksum: 10c0/8e32fb2c576b874a6f59e65b8df4bd54fe04fd026da52c3d1fb9620491bfe893fc4cddb69bc9287e02b025eeb0a8b79ed3dbebe0edb852b23cf6c5cf8f9f8e15
  languageName: node
  linkType: hard

"@expo/prebuild-config@npm:^9.0.11":
  version: 9.0.11
  resolution: "@expo/prebuild-config@npm:9.0.11"
  dependencies:
    "@expo/config": "npm:~11.0.13"
    "@expo/config-plugins": "npm:~10.1.2"
    "@expo/config-types": "npm:^53.0.5"
    "@expo/image-utils": "npm:^0.7.6"
    "@expo/json-file": "npm:^9.1.5"
    "@react-native/normalize-colors": "npm:0.79.5"
    debug: "npm:^4.3.1"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    xml2js: "npm:0.6.0"
  checksum: 10c0/63eb9be6744e2100362c8f7597daf1d4cbfdf4165ae6313c0b5759dbc63e95f8952cfb4634f4e1e0b593e6113100406a1a7972f5231f3fc6c2f7b05c7e0ef708
  languageName: node
  linkType: hard

"@expo/sdk-runtime-versions@npm:^1.0.0":
  version: 1.0.0
  resolution: "@expo/sdk-runtime-versions@npm:1.0.0"
  checksum: 10c0/f80ae78a294daf396f3eff2eb412948ced5501395a6d3b88058866da9c5135dbacbb2804f8d062222e7452159a61eebefd2f548a2939f539f0f0efe8145588a2
  languageName: node
  linkType: hard

"@expo/server@npm:^0.6.3":
  version: 0.6.3
  resolution: "@expo/server@npm:0.6.3"
  dependencies:
    abort-controller: "npm:^3.0.0"
    debug: "npm:^4.3.4"
    source-map-support: "npm:~0.5.21"
    undici: "npm:^6.18.2 || ^7.0.0"
  checksum: 10c0/75e72542345da40e8e7da121b97f048c7587fbeb216cb18e0e66fdba1a0d092c0168be94d05bfda1a9689dfeace59fb0f952eb79fcbd956755c1b32040452b18
  languageName: node
  linkType: hard

"@expo/spawn-async@npm:^1.7.2":
  version: 1.7.2
  resolution: "@expo/spawn-async@npm:1.7.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
  checksum: 10c0/0548c4e95ee39393c2f3919bc605f21eba4f0a8ba66fa82fbbc4b1b624e0054526918489227b924f03af5bc156a011f39a2472c223c0d2237fb7afd8dedd5357
  languageName: node
  linkType: hard

"@expo/sudo-prompt@npm:^9.3.1":
  version: 9.3.2
  resolution: "@expo/sudo-prompt@npm:9.3.2"
  checksum: 10c0/032652bf1c3f326c9c194f336de5821b9ece9d48b22e3e277950d939fcd728c85459680a9771705904d375f128221cca2e1e91c5d7a85cf3c07fe6f88c361e9d
  languageName: node
  linkType: hard

"@expo/vector-icons@npm:^14.0.0, @expo/vector-icons@npm:^14.1.0":
  version: 14.1.0
  resolution: "@expo/vector-icons@npm:14.1.0"
  peerDependencies:
    expo-font: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/f1dcea2c43c0808f48d1953395c6f8025ae5e811648e86b79158492c9ef8af7a40781e42844dfb1434242a08fcf6ab14886825eb2c79bad2a792aebd1eb5077c
  languageName: node
  linkType: hard

"@expo/ws-tunnel@npm:^1.0.1":
  version: 1.0.6
  resolution: "@expo/ws-tunnel@npm:1.0.6"
  checksum: 10c0/050eb7fbd54b636c97c818e7ec5402ce616cae655290386a51600b200947e281cdd12d182251c07fab449e11a732135d61429b738cd03945e94757061e652ecd
  languageName: node
  linkType: hard

"@expo/xcpretty@npm:^4.3.0":
  version: 4.3.2
  resolution: "@expo/xcpretty@npm:4.3.2"
  dependencies:
    "@babel/code-frame": "npm:7.10.4"
    chalk: "npm:^4.1.0"
    find-up: "npm:^5.0.0"
    js-yaml: "npm:^4.1.0"
  bin:
    excpretty: build/cli.js
  checksum: 10c0/e524817b2e42fb8c8914fca7e8f7c2f723f4f6d338a57b7ae97cd3e76da8108af63a22d4c7dc2e96a192a248a242f6e0f8056f0ca53bc4fb5cd2e5ae428e0891
  languageName: node
  linkType: hard

"@gorhom/bottom-sheet@npm:^5.1.6":
  version: 5.1.6
  resolution: "@gorhom/bottom-sheet@npm:5.1.6"
  dependencies:
    "@gorhom/portal": "npm:1.0.14"
    invariant: "npm:^2.2.4"
  peerDependencies:
    "@types/react": "*"
    "@types/react-native": "*"
    react: "*"
    react-native: "*"
    react-native-gesture-handler: ">=2.16.1"
    react-native-reanimated: ">=3.16.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-native":
      optional: true
  checksum: 10c0/2f5c7e4e0a8968a567f5dcac5553b85e09850be46755bcf10a6e44843705392dd71c701b551575d8ea7c6cda8678f4a04851e926b6bfa63e82198b88568a76d8
  languageName: node
  linkType: hard

"@gorhom/portal@npm:1.0.14":
  version: 1.0.14
  resolution: "@gorhom/portal@npm:1.0.14"
  dependencies:
    nanoid: "npm:^3.3.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/86f33afc2ac2656a86a6f3fd1e41565419839576ede2c38333434a93a0a2fe4fb6fc18ab3360579427f2a1fc3b4564b933cc5ae1793a7e2825c93860a00b215f
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^5.1.1":
  version: 5.1.1
  resolution: "@hookform/resolvers@npm:5.1.1"
  dependencies:
    "@standard-schema/utils": "npm:^0.3.0"
  peerDependencies:
    react-hook-form: ^7.55.0
  checksum: 10c0/74601ba4abb3159bbaa25175af9459a2c0337a28d8c0a5be95c7ae7b0a76ddafcf63c03eea8561fd099fe80b226194ad09e3824c53b9beda38393ff9fd264a03
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10c0/3775bb30087d4440b3f7406d5a057777d90e4b9f435af488a4923ef249e93615fb78565a85f173a186a076c7706a81d0d57d563a2624e4de2c5c9c66c486ce42
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@isaacs/ttlcache@npm:^1.4.1":
  version: 1.4.1
  resolution: "@isaacs/ttlcache@npm:1.4.1"
  checksum: 10c0/6921de516917b02673a58e543c2b06fd04237cbf6d089ca22d6e98defa4b1e9a48258cb071d6b581284bb497bea687320788830541511297eecbe6e93a665bbf
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: "npm:^5.3.1"
    find-up: "npm:^4.1.0"
    get-package-type: "npm:^0.1.0"
    js-yaml: "npm:^3.13.1"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/dd2a8b094887da5a1a2339543a4933d06db2e63cbbc2e288eb6431bd832065df0c099d091b6a67436e71b7d6bf85f01ce7c15f9253b4cbebcc3b9a496165ba42
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jest/console@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/console@npm:30.0.4"
  dependencies:
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    jest-message-util: "npm:30.0.2"
    jest-util: "npm:30.0.2"
    slash: "npm:^3.0.0"
  checksum: 10c0/1cc292e56373bb6e6fb2f5670f477068ecc31efed91ff65fd4f60ce3346993ac7264951a950c8134468f993efb5eb0563456294b90755d127f071eb71620d568
  languageName: node
  linkType: hard

"@jest/core@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/core@npm:30.0.4"
  dependencies:
    "@jest/console": "npm:30.0.4"
    "@jest/pattern": "npm:30.0.1"
    "@jest/reporters": "npm:30.0.4"
    "@jest/test-result": "npm:30.0.4"
    "@jest/transform": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.3.2"
    chalk: "npm:^4.1.2"
    ci-info: "npm:^4.2.0"
    exit-x: "npm:^0.2.2"
    graceful-fs: "npm:^4.2.11"
    jest-changed-files: "npm:30.0.2"
    jest-config: "npm:30.0.4"
    jest-haste-map: "npm:30.0.2"
    jest-message-util: "npm:30.0.2"
    jest-regex-util: "npm:30.0.1"
    jest-resolve: "npm:30.0.2"
    jest-resolve-dependencies: "npm:30.0.4"
    jest-runner: "npm:30.0.4"
    jest-runtime: "npm:30.0.4"
    jest-snapshot: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    jest-validate: "npm:30.0.2"
    jest-watcher: "npm:30.0.4"
    micromatch: "npm:^4.0.8"
    pretty-format: "npm:30.0.2"
    slash: "npm:^3.0.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/c33dad78d48497108f32f996ab0af745714e97689c0af7c863c0153dfb463acb2e67143ac1efa4e1a02ded77425d57cae8d9632f3cef6ee065c7d50d6c34b67a
  languageName: node
  linkType: hard

"@jest/create-cache-key-function@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/create-cache-key-function@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
  checksum: 10c0/5c47ef62205264adf77b1ff26b969ce9fe84920b8275c3c5e83f4236859d6ae5e4e7027af99eef04a8e334c4e424d44af3e167972083406070aca733ac2a2795
  languageName: node
  linkType: hard

"@jest/diff-sequences@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/diff-sequences@npm:30.0.1"
  checksum: 10c0/3a840404e6021725ef7f86b11f7b2d13dd02846481264db0e447ee33b7ee992134e402cdc8b8b0ac969d37c6c0183044e382dedee72001cdf50cfb3c8088de74
  languageName: node
  linkType: hard

"@jest/environment@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/environment@npm:30.0.4"
  dependencies:
    "@jest/fake-timers": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    jest-mock: "npm:30.0.2"
  checksum: 10c0/34b5de4ee8833ab490170d2e5cea5c84dd89b9bc6dd6545e811ccf0f09b7bc12f2b0949d6659b36e1c49a5e1597dbe19998cdedf679e65499b20a37ac5be4014
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/c7b1b40c618f8baf4d00609022d2afa086d9c6acc706f303a70bb4b67275868f620ad2e1a9efc5edd418906157337cce50589a627a6400bbdf117d351b91ef86
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/expect-utils@npm:30.0.4"
  dependencies:
    "@jest/get-type": "npm:30.0.1"
  checksum: 10c0/eda2d34b883e72b4ccccac04082701d37d35cc924bba8bbf044578f34257885b04c343fbfa2949831ee75429f665f3b157066025b1e587737b946a64aa75e973
  languageName: node
  linkType: hard

"@jest/expect@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/expect@npm:30.0.4"
  dependencies:
    expect: "npm:30.0.4"
    jest-snapshot: "npm:30.0.4"
  checksum: 10c0/87d0a39cc1aa46d812ed8be3d36c10e9f2536ed92382eeadb418df6eb7161515b3a4698c0b710c60ca9808347e3db16ef99432b9ed25f2eab8c5a70e31985679
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/fake-timers@npm:30.0.4"
  dependencies:
    "@jest/types": "npm:30.0.1"
    "@sinonjs/fake-timers": "npm:^13.0.0"
    "@types/node": "npm:*"
    jest-message-util: "npm:30.0.2"
    jest-mock: "npm:30.0.2"
    jest-util: "npm:30.0.2"
  checksum: 10c0/9c9225088ce85aaf084e4962f1dcea126074d1c5e36f0660feb6efceea8909dce9018561a996fa3e17a441703127171a1b4a01ef3bcdd95639e44303ed92b0cb
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/cf0a8bcda801b28dc2e2b2ba36302200ee8104a45ad7a21e6c234148932f826cb3bc57c8df3b7b815aeea0861d7b6ca6f0d4778f93b9219398ef28749e03595c
  languageName: node
  linkType: hard

"@jest/get-type@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/get-type@npm:30.0.1"
  checksum: 10c0/92437ae42d0df57e8acc2d067288151439db4752cde4f5e680c73c8a6e34568bbd8c1c81a2f2f9a637a619c2aac8bc87553fb80e31475b59e2ed789a71e5e540
  languageName: node
  linkType: hard

"@jest/globals@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/globals@npm:30.0.4"
  dependencies:
    "@jest/environment": "npm:30.0.4"
    "@jest/expect": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    jest-mock: "npm:30.0.2"
  checksum: 10c0/34712f704937621a2188fbcdd439327dc3750e1182745ed3d97e1cbb211d8633781b6647ae5a5cfa56adbfde1ad0c2748041dacef0a8465dbebf38af3c640678
  languageName: node
  linkType: hard

"@jest/pattern@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/pattern@npm:30.0.1"
  dependencies:
    "@types/node": "npm:*"
    jest-regex-util: "npm:30.0.1"
  checksum: 10c0/32c5a7bfb6c591f004dac0ed36d645002ed168971e4c89bd915d1577031672870032594767557b855c5bc330aa1e39a2f54bf150d2ee88a7a0886e9cb65318bc
  languageName: node
  linkType: hard

"@jest/reporters@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/reporters@npm:30.0.4"
  dependencies:
    "@bcoe/v8-coverage": "npm:^0.2.3"
    "@jest/console": "npm:30.0.4"
    "@jest/test-result": "npm:30.0.4"
    "@jest/transform": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    collect-v8-coverage: "npm:^1.0.2"
    exit-x: "npm:^0.2.2"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.11"
    istanbul-lib-coverage: "npm:^3.0.0"
    istanbul-lib-instrument: "npm:^6.0.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^5.0.0"
    istanbul-reports: "npm:^3.1.3"
    jest-message-util: "npm:30.0.2"
    jest-util: "npm:30.0.2"
    jest-worker: "npm:30.0.2"
    slash: "npm:^3.0.0"
    string-length: "npm:^4.0.2"
    v8-to-istanbul: "npm:^9.0.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/aca6a41f50b5bdcf85080934c3371ee5272bce932e1611f2ff22d4a1a6d6faf8d1c414ea4356ee4d49e5ca7e4d861fcfd5c1b4d1876734c6815be338dee459ee
  languageName: node
  linkType: hard

"@jest/schemas@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/schemas@npm:30.0.1"
  dependencies:
    "@sinclair/typebox": "npm:^0.34.0"
  checksum: 10c0/27977359edc4b33293af7c85c53de5014a87c29b9ab98b0a827fedfc6635abdb522aad8c3ff276080080911f519699b094bd6f4e151b43f0cc5856ccc83c04a7
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/b329e89cd5f20b9278ae1233df74016ebf7b385e0d14b9f4c1ad18d096c4c19d1e687aa113a9c976b16ec07f021ae53dea811fb8c1248a50ac34fbe009fdf6be
  languageName: node
  linkType: hard

"@jest/snapshot-utils@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/snapshot-utils@npm:30.0.4"
  dependencies:
    "@jest/types": "npm:30.0.1"
    chalk: "npm:^4.1.2"
    graceful-fs: "npm:^4.2.11"
    natural-compare: "npm:^1.4.0"
  checksum: 10c0/185367ba841eb5becc77cd5a08c0cdbdabebf07160e8477599ae2c482de87bfc2ea584afe12f59697d57ac1fe72975454750e3a5329236899237f7e356041ce4
  languageName: node
  linkType: hard

"@jest/source-map@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/source-map@npm:30.0.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    callsites: "npm:^3.1.0"
    graceful-fs: "npm:^4.2.11"
  checksum: 10c0/e7bda2786fc9f483d9dd7566c58c4bd948830997be862dfe80a3ae5550ff3f84753abb52e705d02ebe9db9f34ba7ebec4c2db11882048cdeef7a66f6332b3897
  languageName: node
  linkType: hard

"@jest/test-result@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/test-result@npm:30.0.4"
  dependencies:
    "@jest/console": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/istanbul-lib-coverage": "npm:^2.0.6"
    collect-v8-coverage: "npm:^1.0.2"
  checksum: 10c0/aeb7e6ac8569e4ea64c29f3dd774182976fb6dfea41c63b2b1a5b8efc5cf8fb37eb4bff5319f20206160fd81fdfc666090f068dc893a6e0eb8a4c42a54907c2b
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/test-sequencer@npm:30.0.4"
  dependencies:
    "@jest/test-result": "npm:30.0.4"
    graceful-fs: "npm:^4.2.11"
    jest-haste-map: "npm:30.0.2"
    slash: "npm:^3.0.0"
  checksum: 10c0/41963e86809329cbdee8380473cf3814518c87adef4ff248f81199ce80122c9615760b68382185c2f5f0b2022f28df6a37ca9821d00ca5ccb6c10a5e75d6fb39
  languageName: node
  linkType: hard

"@jest/transform@npm:30.0.4":
  version: 30.0.4
  resolution: "@jest/transform@npm:30.0.4"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@jest/types": "npm:30.0.1"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    babel-plugin-istanbul: "npm:^7.0.0"
    chalk: "npm:^4.1.2"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.11"
    jest-haste-map: "npm:30.0.2"
    jest-regex-util: "npm:30.0.1"
    jest-util: "npm:30.0.2"
    micromatch: "npm:^4.0.8"
    pirates: "npm:^4.0.7"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^5.0.1"
  checksum: 10c0/8bfe023990e7a30e19bc4b6a4f59f1244bb3eec8d0b756571d3f63c0b50015a2b29905b90759aac79180467616654c5ec0a1b5f14013e7526beda5a030fa651c
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    babel-plugin-istanbul: "npm:^6.1.1"
    chalk: "npm:^4.0.0"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pirates: "npm:^4.0.4"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^4.0.2"
  checksum: 10c0/7f4a7f73dcf45dfdf280c7aa283cbac7b6e5a904813c3a93ead7e55873761fc20d5c4f0191d2019004fac6f55f061c82eb3249c2901164ad80e362e7a7ede5a6
  languageName: node
  linkType: hard

"@jest/types@npm:30.0.1":
  version: 30.0.1
  resolution: "@jest/types@npm:30.0.1"
  dependencies:
    "@jest/pattern": "npm:30.0.1"
    "@jest/schemas": "npm:30.0.1"
    "@types/istanbul-lib-coverage": "npm:^2.0.6"
    "@types/istanbul-reports": "npm:^3.0.4"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.33"
    chalk: "npm:^4.1.2"
  checksum: 10c0/407469331e74f9bb1ffd40202c3a8cece2fd07ba535adeb60557bdcee13713cf2f14cf78869ba7ef50a7e6fe0ed7cc97ec775056dd640fc0a332e8fbfaec1ee8
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10c0/ea4e493dd3fb47933b8ccab201ae573dcc451f951dc44ed2a86123cd8541b82aa9d2b1031caf9b1080d6673c517e2dcc25a44b2dc4f3fbc37bfc965d444888c0
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/32f771ae2467e4d440be609581f7338d786d3d621bac3469e943b9d6d116c23c4becb36f84898a92bbf2f3c0511365c54a945a3b86a83141547a2a360a5ec0c7
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 10c0/c5aab3e6362a8dd94ad80ab90845730c825fc4c8d9cf07ebca7a2eb8a832d155d62558800fc41d42785f989ddbb21db6df004d1786e8ecb65e428ab8dff71309
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.23, @jridgewell/trace-mapping@npm:^0.3.28":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/fb547ba31658c4d74eb17e7389f4908bf7c44cef47acb4c5baa57289daf68e6fe53c639f41f751b3923aca67010501264f70e7b49978ad1f040294b22c37b333
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.11":
  version: 0.2.11
  resolution: "@napi-rs/wasm-runtime@npm:0.2.11"
  dependencies:
    "@emnapi/core": "npm:^1.4.3"
    "@emnapi/runtime": "npm:^1.4.3"
    "@tybys/wasm-util": "npm:^0.9.0"
  checksum: 10c0/049bd14c58b99fbe0967b95e9921c5503df196b59be22948d2155f17652eb305cff6728efd8685338b855da7e476dd2551fbe3a313fc2d810938f0717478441e
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@nolyfill/is-core-module@npm:1.0.39":
  version: 1.0.39
  resolution: "@nolyfill/is-core-module@npm:1.0.39"
  checksum: 10c0/34ab85fdc2e0250879518841f74a30c276bca4f6c3e13526d2d1fe515e1adf6d46c25fcd5989d22ea056d76f7c39210945180b4859fc83b050e2da411aa86289
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.4":
  version: 0.2.7
  resolution: "@pkgr/core@npm:0.2.7"
  checksum: 10c0/951f5ebf2feb6e9dbc202d937f1a364d60f2bf0e3e53594251bcc1d9d2ed0df0a919c49ba162a9499fce73cf46ebe4d7959a8dfbac03511dbe79b69f5fedb804
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d36a9c589eb75d634b9b139c80f916aadaf8a68a7c1c4b8c6c6b88755af1a92f2e343457042089f04cc3f23073619d08bb65419ced1402e9d4e299576d970771
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.0":
  version: 1.2.0
  resolution: "@radix-ui/react-slot@npm:1.2.0"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/f1455f36479e87a0a2254fc2e2b2aba6740d1fbcada949071210bf2a009a031ad508ac01b544bce96337bcca82f49531b46c71615141a5985aaa11ae69b967b1
  languageName: node
  linkType: hard

"@react-native-async-storage/async-storage@npm:2.1.2":
  version: 2.1.2
  resolution: "@react-native-async-storage/async-storage@npm:2.1.2"
  dependencies:
    merge-options: "npm:^3.0.4"
  peerDependencies:
    react-native: ^0.0.0-0 || >=0.65 <1.0
  checksum: 10c0/8f3d6ff1b32ef8705c5c8be8248988cfbfd571c0e8142b8aef15429f13ddc9a018792b4be837215f6592c76b9cd99a931d4f0ab4182eebd8bddede458d484053
  languageName: node
  linkType: hard

"@react-native-community/blur@npm:^4.4.1":
  version: 4.4.1
  resolution: "@react-native-community/blur@npm:4.4.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/f8f3b2e0dc630d93219c5db822a5d283754beedf31b8e481c17bca6f8b2a29d0e24cad4c5013f692f120a3336490dc5b1b3990a68c714c3308151adf92686ad7
  languageName: node
  linkType: hard

"@react-native-community/datetimepicker@npm:8.4.1":
  version: 8.4.1
  resolution: "@react-native-community/datetimepicker@npm:8.4.1"
  dependencies:
    invariant: "npm:^2.2.4"
  peerDependencies:
    expo: ">=52.0.0"
    react: "*"
    react-native: "*"
    react-native-windows: "*"
  peerDependenciesMeta:
    expo:
      optional: true
    react-native-windows:
      optional: true
  checksum: 10c0/f76b07ba793b17a91fba6023a37de07d0989f2648a95951d163ae140fd42b50fb33381557de4e99c0713b653c2cb21b814b18623a4df5656df0dce724dc5f055
  languageName: node
  linkType: hard

"@react-native-community/netinfo@npm:^11.4.1":
  version: 11.4.1
  resolution: "@react-native-community/netinfo@npm:11.4.1"
  peerDependencies:
    react-native: ">=0.59"
  checksum: 10c0/118ba6d495bd14c2dab3d228a0c1857620a293ae8592d170d70eaf88a6ac6a4e35c7f84527975a9054e36891a9694aacc8ec053326b5f05961459ed08a70dd35
  languageName: node
  linkType: hard

"@react-native-picker/picker@npm:^2.11.1":
  version: 2.11.1
  resolution: "@react-native-picker/picker@npm:2.11.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/d2e9aeb32ae80f2ab56fa025f1afd6d8ff8051e7078bfd17c6794ff0cc1538d4a2f548098826eb652e6cb64d998070b0111777c78de4dcb468cb71e7e4d93097
  languageName: node
  linkType: hard

"@react-native/assets-registry@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/assets-registry@npm:0.79.5"
  checksum: 10c0/629d2062154ac9a95ab9832bbea8d63cb27911d6b1d5b267461cffd4760c6e02bb1a8b1e060127e961a37cf53d311f2efffeb4a485bfbf6fa832c90e2cf23ea2
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/babel-plugin-codegen@npm:0.79.5"
  dependencies:
    "@babel/traverse": "npm:^7.25.3"
    "@react-native/codegen": "npm:0.79.5"
  checksum: 10c0/06ce614d4a5f6d7bb6fe8f51f8c1db48e057465121b85efbd26926b3d44fc23f47b86ee49859c6c200a465f3e0cc62fd8b73d8c966566ac7760b759772853637
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/babel-preset@npm:0.79.5"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/plugin-proposal-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.8.3"
    "@babel/plugin-syntax-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-transform-arrow-functions": "npm:^7.24.7"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.25.4"
    "@babel/plugin-transform-async-to-generator": "npm:^7.24.7"
    "@babel/plugin-transform-block-scoping": "npm:^7.25.0"
    "@babel/plugin-transform-class-properties": "npm:^7.25.4"
    "@babel/plugin-transform-classes": "npm:^7.25.4"
    "@babel/plugin-transform-computed-properties": "npm:^7.24.7"
    "@babel/plugin-transform-destructuring": "npm:^7.24.8"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.25.2"
    "@babel/plugin-transform-for-of": "npm:^7.24.7"
    "@babel/plugin-transform-function-name": "npm:^7.25.1"
    "@babel/plugin-transform-literals": "npm:^7.25.2"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.24.7"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.24.8"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.24.7"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.24.7"
    "@babel/plugin-transform-numeric-separator": "npm:^7.24.7"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.24.7"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.24.7"
    "@babel/plugin-transform-optional-chaining": "npm:^7.24.8"
    "@babel/plugin-transform-parameters": "npm:^7.24.7"
    "@babel/plugin-transform-private-methods": "npm:^7.24.7"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.24.7"
    "@babel/plugin-transform-react-display-name": "npm:^7.24.7"
    "@babel/plugin-transform-react-jsx": "npm:^7.25.2"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.24.7"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.24.7"
    "@babel/plugin-transform-regenerator": "npm:^7.24.7"
    "@babel/plugin-transform-runtime": "npm:^7.24.7"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.24.7"
    "@babel/plugin-transform-spread": "npm:^7.24.7"
    "@babel/plugin-transform-sticky-regex": "npm:^7.24.7"
    "@babel/plugin-transform-typescript": "npm:^7.25.2"
    "@babel/plugin-transform-unicode-regex": "npm:^7.24.7"
    "@babel/template": "npm:^7.25.0"
    "@react-native/babel-plugin-codegen": "npm:0.79.5"
    babel-plugin-syntax-hermes-parser: "npm:0.25.1"
    babel-plugin-transform-flow-enums: "npm:^0.0.2"
    react-refresh: "npm:^0.14.0"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/5c51a2e196f89c4a2266cdfa7d7324cf4511c6ec8bffcd3422290a03ecdbb16fceee81b37a9b2263a70e46c5816b331f97a227191b5360aa9b8739927448a4ba
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/codegen@npm:0.79.5"
  dependencies:
    glob: "npm:^7.1.1"
    hermes-parser: "npm:0.25.1"
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/50c28e3f8cb1e3c03323f848fc94edb190fc1130a3f037c40030f0cd930c5b26941b7e5e3d48c1536cf397866515d618ab8ad62e5bcd84dc81e32e8ace8cacc3
  languageName: node
  linkType: hard

"@react-native/community-cli-plugin@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/community-cli-plugin@npm:0.79.5"
  dependencies:
    "@react-native/dev-middleware": "npm:0.79.5"
    chalk: "npm:^4.0.0"
    debug: "npm:^2.2.0"
    invariant: "npm:^2.2.4"
    metro: "npm:^0.82.0"
    metro-config: "npm:^0.82.0"
    metro-core: "npm:^0.82.0"
    semver: "npm:^7.1.3"
  peerDependencies:
    "@react-native-community/cli": "*"
  peerDependenciesMeta:
    "@react-native-community/cli":
      optional: true
  checksum: 10c0/03f95a0f2044dd3b28da8d2ae38794651e2e12587264d6bceffc4e61545052232ca8e42bd5f794b5af24dd1cd9fdb83992aaa7c7679c4888b65bf497903c06ea
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/debugger-frontend@npm:0.79.5"
  checksum: 10c0/486e36e1358714de4d9b40f665a29397beca80b7521343e850124b0380022c1c8c5b8f6cf22920b1cd6d938fbab53426d18e748fb2eaa0abd0e38832858a8f0f
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/dev-middleware@npm:0.79.5"
  dependencies:
    "@isaacs/ttlcache": "npm:^1.4.1"
    "@react-native/debugger-frontend": "npm:0.79.5"
    chrome-launcher: "npm:^0.15.2"
    chromium-edge-launcher: "npm:^0.2.0"
    connect: "npm:^3.6.5"
    debug: "npm:^2.2.0"
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
    open: "npm:^7.0.3"
    serve-static: "npm:^1.16.2"
    ws: "npm:^6.2.3"
  checksum: 10c0/85a2fe06e74d2fd486c55cde268a96dc24fda6a8d7be2181d15dfe9d6065f352b7a17693456138923b0dcf1362ce0bf61e34a127686204269b8ccbbe15ea34d2
  languageName: node
  linkType: hard

"@react-native/gradle-plugin@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/gradle-plugin@npm:0.79.5"
  checksum: 10c0/ca0d144dfd2fba0a3d550a6bb273e67e8d5b058fd616c75f358390fa7d4d8ff6e696be30b164f8427f776dcc60f80822f0e7be0799419772415e1f84536f479c
  languageName: node
  linkType: hard

"@react-native/js-polyfills@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/js-polyfills@npm:0.79.5"
  checksum: 10c0/49b9a51d882fb5aadbfbb120624e16053ad8146d82ae9fc9f9cdecde6304cc9fd9a2183f9300eefafdd867a1688b3d9ccd3721b18fa6c58d998e8c5733c91d9b
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/normalize-colors@npm:0.79.5"
  checksum: 10c0/d5b97f3ff7c0f6117ab92ae888611f2f7d6aa7f090e44f72658a7103582cc7d47fbb56c9e61fb9c6b44186c97b0f165425c032cf8d217ebf8955fe9f7ac41dea
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:^0.74.1":
  version: 0.74.89
  resolution: "@react-native/normalize-colors@npm:0.74.89"
  checksum: 10c0/6d0e5c91793ca5a66b4a0e5995361f474caacac56bde4772ac02b8ab470bd323076c567bd8856b0b097816d2b890e73a4040a3df01fd284adee683f5ba89d5ba
  languageName: node
  linkType: hard

"@react-native/virtualized-lists@npm:0.79.5":
  version: 0.79.5
  resolution: "@react-native/virtualized-lists@npm:0.79.5"
  dependencies:
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
  peerDependencies:
    "@types/react": ^19.0.0
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/7a5e35e523bd847f0df6430af97216bcc66149d138cb27d1c8ab9ecc8887e8f5d9f7821f972d980a75b4fa9f88f350a3715b54132fe24dc649dea4706c10d09b
  languageName: node
  linkType: hard

"@react-navigation/bottom-tabs@npm:^7.3.10":
  version: 7.3.14
  resolution: "@react-navigation/bottom-tabs@npm:7.3.14"
  dependencies:
    "@react-navigation/elements": "npm:^2.4.3"
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-navigation/native": ^7.1.10
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/ad91443b7a66be3d9de4aba6f11a7f61cc142b1f594643f1787517bc6da5ef715ca9e61391c545ecd7fc7cda3f566a9f24d1e54046e62551d5bd1a919fbe718a
  languageName: node
  linkType: hard

"@react-navigation/bottom-tabs@npm:^7.4.2":
  version: 7.4.2
  resolution: "@react-navigation/bottom-tabs@npm:7.4.2"
  dependencies:
    "@react-navigation/elements": "npm:^2.5.2"
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-navigation/native": ^7.1.14
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/17ba09e355e9a106f9431d5fedb2c4716f0f47e2b87e8bf7927e153d9fb799104f3f2a0f8eab527ead4f9a697fd6e771c998c724cf53e632109ff5c6440d73c3
  languageName: node
  linkType: hard

"@react-navigation/core@npm:^7.10.0":
  version: 7.10.0
  resolution: "@react-navigation/core@npm:7.10.0"
  dependencies:
    "@react-navigation/routers": "npm:^7.4.0"
    escape-string-regexp: "npm:^4.0.0"
    nanoid: "npm:^3.3.11"
    query-string: "npm:^7.1.3"
    react-is: "npm:^19.1.0"
    use-latest-callback: "npm:^0.2.3"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ">= 18.2.0"
  checksum: 10c0/f10a00a2a8e41b89cb576240404bf6b1dbbe58ef15f86436fe0a0e3ec68e463bdbbc77a6d23ba702de572bf7bb33f0e9fc97e59d68179b4d512e54f23914f23d
  languageName: node
  linkType: hard

"@react-navigation/core@npm:^7.12.1":
  version: 7.12.1
  resolution: "@react-navigation/core@npm:7.12.1"
  dependencies:
    "@react-navigation/routers": "npm:^7.4.1"
    escape-string-regexp: "npm:^4.0.0"
    nanoid: "npm:^3.3.11"
    query-string: "npm:^7.1.3"
    react-is: "npm:^19.1.0"
    use-latest-callback: "npm:^0.2.4"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ">= 18.2.0"
  checksum: 10c0/ccb695c34b1d8c963cefed721d91ea85108dbd3a011f2f5434b4b73d45671db0883932885bf95c7855e715f3a485f32685bf788b9c2a9c107fd5035ce875dcfd
  languageName: node
  linkType: hard

"@react-navigation/drawer@npm:^7.3.9":
  version: 7.5.2
  resolution: "@react-navigation/drawer@npm:7.5.2"
  dependencies:
    "@react-navigation/elements": "npm:^2.5.2"
    color: "npm:^4.2.3"
    react-native-drawer-layout: "npm:^4.1.11"
    use-latest-callback: "npm:^0.2.4"
  peerDependencies:
    "@react-navigation/native": ^7.1.14
    react: ">= 18.2.0"
    react-native: "*"
    react-native-gesture-handler: ">= 2.0.0"
    react-native-reanimated: ">= 2.0.0"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/601116996263c1851c0a3a87d04b65aec4b6f558feb8e3ed2e12dd692d0546ce059fc2d25f8343fbd8ee2a5edcc963b7b5ba64cce8365d4ac789e82c46524651
  languageName: node
  linkType: hard

"@react-navigation/elements@npm:^2.4.3":
  version: 2.4.3
  resolution: "@react-navigation/elements@npm:2.4.3"
  dependencies:
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-native-masked-view/masked-view": ">= 0.2.0"
    "@react-navigation/native": ^7.1.10
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
  peerDependenciesMeta:
    "@react-native-masked-view/masked-view":
      optional: true
  checksum: 10c0/4cd25c0373fd8fef9d1eb830fe210743e9a7cb6664649912702137e8541811887e44e64b5d6bfac73b3a4e4903d701076611d802a40668928ab2c793ad399e04
  languageName: node
  linkType: hard

"@react-navigation/elements@npm:^2.5.2":
  version: 2.5.2
  resolution: "@react-navigation/elements@npm:2.5.2"
  dependencies:
    color: "npm:^4.2.3"
    use-latest-callback: "npm:^0.2.4"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    "@react-native-masked-view/masked-view": ">= 0.2.0"
    "@react-navigation/native": ^7.1.14
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
  peerDependenciesMeta:
    "@react-native-masked-view/masked-view":
      optional: true
  checksum: 10c0/d019665639ba3510fd7cebf7ba67bfc9b64e8041ae4e7e7dfb8fef280ac37884c9e9620e20e1c448e828231779216fb34caf9d53c43b49a9f679e60cbe171b05
  languageName: node
  linkType: hard

"@react-navigation/native-stack@npm:^7.3.10":
  version: 7.3.14
  resolution: "@react-navigation/native-stack@npm:7.3.14"
  dependencies:
    "@react-navigation/elements": "npm:^2.4.3"
    warn-once: "npm:^0.1.1"
  peerDependencies:
    "@react-navigation/native": ^7.1.10
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/46fd08320e9aeffa95c373671380bb086075b54fb17d370ad468f5f439a5959972ecb549b3226c6af572a5749ef14a59cb8bbb6bb535ffb865d0a524cebd9c67
  languageName: node
  linkType: hard

"@react-navigation/native@npm:^7.1.14":
  version: 7.1.14
  resolution: "@react-navigation/native@npm:7.1.14"
  dependencies:
    "@react-navigation/core": "npm:^7.12.1"
    escape-string-regexp: "npm:^4.0.0"
    fast-deep-equal: "npm:^3.1.3"
    nanoid: "npm:^3.3.11"
    use-latest-callback: "npm:^0.2.4"
  peerDependencies:
    react: ">= 18.2.0"
    react-native: "*"
  checksum: 10c0/304c34b2051761a9b98e7c70cd86816c5f142841bff7a7386cb4626111c057a20b96667e0dc4da8df1acf3969a291c7c1903244229a75e61b96c8f279a339047
  languageName: node
  linkType: hard

"@react-navigation/native@npm:^7.1.6":
  version: 7.1.10
  resolution: "@react-navigation/native@npm:7.1.10"
  dependencies:
    "@react-navigation/core": "npm:^7.10.0"
    escape-string-regexp: "npm:^4.0.0"
    fast-deep-equal: "npm:^3.1.3"
    nanoid: "npm:^3.3.11"
    use-latest-callback: "npm:^0.2.3"
  peerDependencies:
    react: ">= 18.2.0"
    react-native: "*"
  checksum: 10c0/d1fe316ed86ed04280c21d0225f9f170339eac22803e26114ba57a6d1514f68777b7971c9c80872f98267c4d5c068c3ea13bcc525333ac132021d5031046f011
  languageName: node
  linkType: hard

"@react-navigation/routers@npm:^7.4.0":
  version: 7.4.0
  resolution: "@react-navigation/routers@npm:7.4.0"
  dependencies:
    nanoid: "npm:^3.3.11"
  checksum: 10c0/87e423fe0854e405471d2c11a2fc09f1be06c36f0b08db4dc12afc0c44441251aa26e262f51d69504b0e9bf6829198ddd222a2364f242b2023325fc35377ba4a
  languageName: node
  linkType: hard

"@react-navigation/routers@npm:^7.4.1":
  version: 7.4.1
  resolution: "@react-navigation/routers@npm:7.4.1"
  dependencies:
    nanoid: "npm:^3.3.11"
  checksum: 10c0/785c061df9aa0239251fe4942f0d8906d9448ea96ab3cda06e111aa94295094358453dd22ca574aef19f65618315ed1ad159acb567db5a83d37b70bcd7923814
  languageName: node
  linkType: hard

"@react-navigation/stack@npm:^7.4.2":
  version: 7.4.2
  resolution: "@react-navigation/stack@npm:7.4.2"
  dependencies:
    "@react-navigation/elements": "npm:^2.5.2"
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-navigation/native": ^7.1.14
    react: ">= 18.2.0"
    react-native: "*"
    react-native-gesture-handler: ">= 2.0.0"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/736e12f59175637c510f83bfa55057b8e6719521b4f77e1ac8b7b22dbae7b02d503b4f8aee29ef05d33f0cd4d352de629c3d6520b6dc9c283fe9775fa798963d
  languageName: node
  linkType: hard

"@reduxjs/toolkit@npm:^2.8.2":
  version: 2.8.2
  resolution: "@reduxjs/toolkit@npm:2.8.2"
  dependencies:
    "@standard-schema/spec": "npm:^1.0.0"
    "@standard-schema/utils": "npm:^0.3.0"
    immer: "npm:^10.0.3"
    redux: "npm:^5.0.1"
    redux-thunk: "npm:^3.1.0"
    reselect: "npm:^5.1.0"
  peerDependencies:
    react: ^16.9.0 || ^17.0.0 || ^18 || ^19
    react-redux: ^7.2.1 || ^8.1.3 || ^9.0.0
  peerDependenciesMeta:
    react:
      optional: true
    react-redux:
      optional: true
  checksum: 10c0/6a7a33bad5f1100340757151ff86ca0c4c248f030ae56ce0e6f1d98b39fa87c8f193e9faa2ebd6d5a4c0416921e9f9f7a2bbdd81156c39f08f6bf5ce70c2b927
  languageName: node
  linkType: hard

"@rn-primitives/avatar@npm:^1.2.0":
  version: 1.2.0
  resolution: "@rn-primitives/avatar@npm:1.2.0"
  dependencies:
    "@rn-primitives/hooks": "npm:1.3.0"
    "@rn-primitives/slot": "npm:1.2.0"
    "@rn-primitives/types": "npm:1.2.0"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native:
      optional: true
    react-native-web:
      optional: true
  checksum: 10c0/4fad85ae618b9b36c6e93a87867427b05dc5ad6fc8d8ff58ace5afc054c372ad0f63a5438eb28a620ca3797d14205efeac83205bc00d6e852de5fefba2b30293
  languageName: node
  linkType: hard

"@rn-primitives/hooks@npm:1.3.0":
  version: 1.3.0
  resolution: "@rn-primitives/hooks@npm:1.3.0"
  dependencies:
    "@rn-primitives/types": "npm:1.2.0"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native:
      optional: true
    react-native-web:
      optional: true
  checksum: 10c0/49f5676b97f1adb8a1c39dfe7a976df905aafb7dcec9eaba6bb1aa10f5abda9676fcd972565b0dc9aece39f4d4bda2f7d40e4292ff71d2dabddcbf5d0ec1c473
  languageName: node
  linkType: hard

"@rn-primitives/slot@npm:1.2.0, @rn-primitives/slot@npm:^1.2.0":
  version: 1.2.0
  resolution: "@rn-primitives/slot@npm:1.2.0"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native:
      optional: true
    react-native-web:
      optional: true
  checksum: 10c0/ffcafb5ff7c7c104067e9f0bd9ea3516eec902db1ef7dfb3dc7a194bd3704ae6d3d227f4f83e3ead98ed40c644850fcf235db933848ba66b0a78874bf46ea1dd
  languageName: node
  linkType: hard

"@rn-primitives/types@npm:1.2.0":
  version: 1.2.0
  resolution: "@rn-primitives/types@npm:1.2.0"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native:
      optional: true
    react-native-web:
      optional: true
  checksum: 10c0/059ef0662797bebb491f369e0ab4ee871e154cf6e156f7808e637ddba29498a13fa44d298b67090552fa74142577361d25ef48aa573509387db6c71adad5c738
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 10c0/b5bcfb0d87f7d1c1c7c0f7693f53b07866ed9fec4c34a97a8c948fb9a7c0082e416ce4d3b60beb4f5e167cbe04cdeefbf6771320f3ede059b9ce91188c409a5b
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.34.0":
  version: 0.34.37
  resolution: "@sinclair/typebox@npm:0.34.37"
  checksum: 10c0/22fff01853d8f35e8a1f0be004e91a0c3ced16f35b8d7e915392e91bf021190bcba45102cd148679c53440c4ed228b31d7a2635461ea5d089ef581f6254ecfb4
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0, @sinonjs/commons@npm:^3.0.1":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10c0/1227a7b5bd6c6f9584274db996d7f8cee2c8c350534b9d0141fc662eaf1f292ea0ae3ed19e5e5271c8fd390d27e492ca2803acd31a1978be2cdc6be0da711403
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10c0/2e2fb6cc57f227912814085b7b01fede050cd4746ea8d49a1e44d5a0e56a804663b0340ae2f11af7559ea9bf4d087a11f2f646197a660ea3cb04e19efc04aa63
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^13.0.0":
  version: 13.0.5
  resolution: "@sinonjs/fake-timers@npm:13.0.5"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.1"
  checksum: 10c0/a707476efd523d2138ef6bba916c83c4a377a8372ef04fad87499458af9f01afc58f4f245c5fd062793d6d70587309330c6f96947b5bd5697961c18004dc3e26
  languageName: node
  linkType: hard

"@standard-schema/spec@npm:^1.0.0":
  version: 1.0.0
  resolution: "@standard-schema/spec@npm:1.0.0"
  checksum: 10c0/a1ab9a8bdc09b5b47aa8365d0e0ec40cc2df6437be02853696a0e377321653b0d3ac6f079a8c67d5ddbe9821025584b1fb71d9cc041a6666a96f1fadf2ece15f
  languageName: node
  linkType: hard

"@standard-schema/utils@npm:^0.3.0":
  version: 0.3.0
  resolution: "@standard-schema/utils@npm:0.3.0"
  checksum: 10c0/6eb74cd13e52d5fc74054df51e37d947ef53f3ab9e02c085665dcca3c38c60ece8d735cebbdf18fbb13c775fbcb9becb3f53109b0e092a63f0f7389ce0993fd0
  languageName: node
  linkType: hard

"@tabler/icons-react-native@npm:^3.34.0":
  version: 3.34.0
  resolution: "@tabler/icons-react-native@npm:3.34.0"
  dependencies:
    "@tabler/icons": "npm:3.34.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0
  checksum: 10c0/de1f98b7028f075c1964e14e7c52a6b136bfe12c7f5bee29e6cf264fa540d21a9fdba55be7e791e88915d78f69cd701d0fee1e735f7ac4124839736fd6fbf70c
  languageName: node
  linkType: hard

"@tabler/icons@npm:3.34.0":
  version: 3.34.0
  resolution: "@tabler/icons@npm:3.34.0"
  checksum: 10c0/7b4bb937e051ece9258ab4321363f284a4c1d53fbe0211c6976bff41d0ca031e8196156595bb30fa2bfb963f1771814d1438aa01a4422df9a6e21a46ebe6cc9b
  languageName: node
  linkType: hard

"@testing-library/react-native@npm:^13.2.0":
  version: 13.2.0
  resolution: "@testing-library/react-native@npm:13.2.0"
  dependencies:
    chalk: "npm:^4.1.2"
    jest-matcher-utils: "npm:^29.7.0"
    pretty-format: "npm:^29.7.0"
    redent: "npm:^3.0.0"
  peerDependencies:
    jest: ">=29.0.0"
    react: ">=18.2.0"
    react-native: ">=0.71"
    react-test-renderer: ">=18.2.0"
  peerDependenciesMeta:
    jest:
      optional: true
  checksum: 10c0/5ed8e09f82f45c057f12a716008f31abf934e6a3d84955540e2ab96d7534c82b9afdb0af050e986d8b63ae9dd8272f8a752c45ecb847a11e7549f30de3d84427
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.9.0":
  version: 0.9.0
  resolution: "@tybys/wasm-util@npm:0.9.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/f9fde5c554455019f33af6c8215f1a1435028803dc2a2825b077d812bed4209a1a64444a4ca0ce2ea7e1175c8d88e2f9173a36a33c199e8a5c671aa31de8242d
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14, @types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/5386f0af44f8746b063b87418f06129a814e16bb2686965a575e9d7376b360b088b89177778d8c426012abc43dd1a2d8ec3218bfc382280c898682746ce2ffbd
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/235d2fc69741448e853333b7c3d1180a966dd2b8972c8cbcd6b2a0c6cd7f8d582ab2b8e58219dbc62cce8f1b40aa317ff78ea2201cdd8249da5025adebed6f0b
  languageName: node
  linkType: hard

"@types/hammerjs@npm:^2.0.36":
  version: 2.0.46
  resolution: "@types/hammerjs@npm:2.0.46"
  checksum: 10c0/f3c1cb20dc2f0523f7b8c76065078544d50d8ae9b0edc1f62fed657210ed814266ff2dfa835d2c157a075991001eec3b64c88bf92e3e6e895c0db78d05711d06
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1, @types/istanbul-lib-coverage@npm:^2.0.6":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10c0/3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0, @types/istanbul-reports@npm:^3.0.4":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/jest@npm:^30.0.0":
  version: 30.0.0
  resolution: "@types/jest@npm:30.0.0"
  dependencies:
    expect: "npm:^30.0.0"
    pretty-format: "npm:^30.0.0"
  checksum: 10c0/20c6ce574154bc16f8dd6a97afacca4b8c4921a819496a3970382031c509ebe87a1b37b152a1b8475089b82d8ca951a9e95beb4b9bf78fbf579b1536f0b65969
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 10c0/6bf5337bc447b706bb5b4431d37686aa2ea6d07cfd6f79cc31de80170d6ff9b1c7384a9c0ccbc45b3f512bae9e9f75c2e12109806a15331dc94e8a8db6dbb4ac
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 24.0.4
  resolution: "@types/node@npm:24.0.4"
  dependencies:
    undici-types: "npm:~7.8.0"
  checksum: 10c0/590e8cb0ec59fb9cd566402120e690d87ecbdf57f1ee2b8493266121ed33aa4b25949a0c6156b84a6ffb9250baaf1f80e9af142da542ed603e6ee73fc4d1115f
  languageName: node
  linkType: hard

"@types/react-native-crypto-js@npm:^1":
  version: 1.0.3
  resolution: "@types/react-native-crypto-js@npm:1.0.3"
  checksum: 10c0/844038c6790342651b56cb46133b543fb58607e38a995f5c30d3eb17ae3943744d67afc2fc5fc193ca3f49e177f800fba939110ef5adc9195980d2b6a1042bc6
  languageName: node
  linkType: hard

"@types/react-test-renderer@npm:^19":
  version: 19.1.0
  resolution: "@types/react-test-renderer@npm:19.1.0"
  dependencies:
    "@types/react": "npm:*"
  checksum: 10c0/799654e430df10aeaf267d71507fb64ec151359ead7e3774111bfd4abce7e0911dba461811195c06c22a6d17496ea92537d3185320ff4112fe29954cad1b9152
  languageName: node
  linkType: hard

"@types/react@npm:*, @types/react@npm:>=16.0.0":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/4908772be6dc941df276931efeb0e781777fa76e4d5d12ff9f75eb2dcc2db3065e0100efde16fde562c5bafa310cc8f50c1ee40a22640459e066e72cd342143e
  languageName: node
  linkType: hard

"@types/react@npm:~19.0.10":
  version: 19.0.14
  resolution: "@types/react@npm:19.0.14"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/e5d9ac42fc6d66c21b7020c8ae1a8190c486e63e5daf2f67b67694dd39c6264cc92a57f90b84525bf73774b90f91bd3b1d907022bcc9b36d6d4ffbcf001f8feb
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0, @types/stack-utils@npm:^2.0.3":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10c0/1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.6":
  version: 0.0.6
  resolution: "@types/use-sync-external-store@npm:0.0.6"
  checksum: 10c0/77c045a98f57488201f678b181cccd042279aff3da34540ad242f893acc52b358bd0a8207a321b8ac09adbcef36e3236944390e2df4fcedb556ce7bb2a88f2a8
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10c0/e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.33, @types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^8.18.2":
  version: 8.34.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.34.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/type-utils": "npm:8.34.0"
    "@typescript-eslint/utils": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.34.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/905a05d15f4b0367838ec445f9890321d87470198bf7a589278fc0f38c82cf3ccc1efce4acd3c9c94ee6149d5579ef58606fb7c50f4db50c830de65af8c27c6d
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^8.18.2":
  version: 8.34.0
  resolution: "@typescript-eslint/parser@npm:8.34.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/a829be00ea3455c1e50983c8b44476fbfc9329d019764e407c4d591a95dbd168f83f13e309751242bb4fdc02f89cb51ca5cdc912a12b10f69eebcb1c46dcc39b
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/project-service@npm:8.34.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.34.0"
    "@typescript-eslint/types": "npm:^8.34.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/88e64b8daf7db9603277fcbeb9e585e70ec6d6e34fa10d4b60f421e48081cc7c1f6acb01e1ee9dd95e10c0601f164c1defbfe6c9d1edc9822089bb72dbb0fc80
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/scope-manager@npm:8.34.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
  checksum: 10c0/35af36bddc4c227cb0bac42192c40b38179ced30866b6aac642781e21c3f3b1c72051eb4f685d7c99517c3296dd6ba83dd8360e4072e8dcf604aae266eece1b4
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.34.0, @typescript-eslint/tsconfig-utils@npm:^8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.34.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/98246f89d169d3feb453a6a8552c51d10225cb00c4ff1501549b7846e564ad0e218b644cd94ce779dceed07dcb9035c53fd32186b4c0223b7b2a1f7295b120c3
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/type-utils@npm:8.34.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
    "@typescript-eslint/utils": "npm:8.34.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/7c25d7f4186411190142390467160e81384d400cfb21183d8a305991c723da0a74e5528cdce30b5f2cb6d9d2f6af7c0981c20c18b45fc084b35632429270ae80
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.34.0, @typescript-eslint/types@npm:^8.29.1, @typescript-eslint/types@npm:^8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/types@npm:8.34.0"
  checksum: 10c0/5d32b2ac03e4cbc1ac1777a53ee83d6d7887a783363bab4f0a6f7550a9e9df0254971cdf71e13b988e2215f2939e7592404856b8acb086ec63c4479c0225c742
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.34.0"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.34.0"
    "@typescript-eslint/tsconfig-utils": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/e678982b0009e895aee2b4ccc55bb9ea5473a32e846a97c63d0c6a978c72e1a29e506e6a5f9dda45e9b7803e6c3e3abcdf4c316af1c59146abef4e10e0e94129
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.34.0, @typescript-eslint/utils@npm:^8.29.1":
  version: 8.34.0
  resolution: "@typescript-eslint/utils@npm:8.34.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/d759cf6f1b1b23d7d8ab922345e7b68b7c829f4bad841164312cfa3a3e8e818b962dd0d96c1aca7fd7c10248d56538d9714df5f3cfec9f159ca0a139feac60b9
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.34.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.34.0"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/d50997e921a178589913d08ffe14d02eba40666c90bdc0c9751f2b87ce500598f64027e2d866dfc975647b2f8b907158503d0722d6b1976c8f1cf5dd8e1d6d69
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.3.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm-eabi@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-android-arm-eabi@npm:1.11.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-android-arm64@npm:1.11.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.11.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.8.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.11.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.8.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.11.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.8.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.11.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.8.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.11.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.8.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.8.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.11.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.8.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.8.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.8.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.11.1"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.8.1"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.11.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.8.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.11.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.8.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.11.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.8.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.11.1"
  dependencies:
    "@napi-rs/wasm-runtime": "npm:^0.2.11"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.8.1"
  dependencies:
    "@napi-rs/wasm-runtime": "npm:^0.2.11"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.8.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.8.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.11.1":
  version: 1.11.1
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.11.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.8.1":
  version: 1.8.1
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.8.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@urql/core@npm:^5.0.6, @urql/core@npm:^5.1.1":
  version: 5.1.1
  resolution: "@urql/core@npm:5.1.1"
  dependencies:
    "@0no-co/graphql.web": "npm:^1.0.5"
    wonka: "npm:^6.3.2"
  checksum: 10c0/2a66f58452bbf153c251dd6d127fc0bc0473b4cde47171ca360960059eb08fc019202aee16911168a800814a3b9748300bb88b87817b5d05cf92c16f5772447b
  languageName: node
  linkType: hard

"@urql/exchange-retry@npm:^1.3.0":
  version: 1.3.1
  resolution: "@urql/exchange-retry@npm:1.3.1"
  dependencies:
    "@urql/core": "npm:^5.1.1"
    wonka: "npm:^6.3.2"
  peerDependencies:
    "@urql/core": ^5.0.0
  checksum: 10c0/c7d0e5e31de3ad3ff169ca5a2f635be03057dd151a9d2ef7ff2c8a605c4d1129f8a2b7671fc9adf90aef69c963a0cc08d32c62f8fe60f220d412757e71916806
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:^1.3.7, accepts@npm:^1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10c0/e43ba22e91b6a48d96224b83d260d3a3a561b42d391f8d3c6d2c1559f9aa5b253bfb306bc94bbeca1d967c014e15a6efe9a207309e95b3eaae07fcbcdc2af662
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10c0/18bec51f0171b83123ba1d8883c126e60c6f420cef885250898bf77a8d3e65e3bfb9e8564f497e30bdbe762a83e0d144a36931328616a973ee669dc74d4a9590
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"anser@npm:^1.4.9":
  version: 1.4.10
  resolution: "anser@npm:1.4.10"
  checksum: 10c0/ab251c96f6b9b8858e346137b75968ef3d287e10f358cd3981666949093e587defb5f7059a05a929eb44e1b3775bae346a55ab952e74049355e70f81b8b1ef53
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1, ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: 10c0/d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0, ansi-styles@npm:^5.2.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 10c0/60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:^3.1.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 10c0/ccaf86f4e05d342af6666c569f844bec426595c567d32a8289715087825c2ca7edd8a3d204e4d2fb2aa4602e09a57d0c13ea8c9eea75aac3dbb4af5514e6800e
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.24.0"
    es-object-atoms: "npm:^1.1.1"
    get-intrinsic: "npm:^1.3.0"
    is-string: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/0235fa69078abeac05ac4250699c44996bc6f774a9cbe45db48674ce6bd142f09b327d31482ff75cf03344db4ea03eae23edb862d59378b484b47ed842574856
  languageName: node
  linkType: hard

"array-timsort@npm:^1.0.3":
  version: 1.0.3
  resolution: "array-timsort@npm:1.0.3"
  checksum: 10c0/bd3a1707b621947265c89867e67c9102b9b9f4c50f5b3974220112290d8b60d26ce60595edec5deed3325207b759d70b758bed3cd310b5ddadb835657ffb6d12
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ddc952b829145ab45411b9d6adcb51a8c17c76bf89c9dd64b52d5dffa65d033da8c076ed2e17091779e83bc892b9848188d7b4b33453c5565e65a92863cb2775
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-shim-unscopables: "npm:^1.1.0"
  checksum: 10c0/82559310d2e57ec5f8fc53d7df420e3abf0ba497935de0a5570586035478ba7d07618cb18e2d4ada2da514c8fb98a034aaf5c06caa0a57e2f7f4c4adedef5956
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/d90e04dfbc43bb96b3d2248576753d1fb2298d2d972e29ca7ad5ec621f0d9e16ff8074dae647eac4f31f4fb7d3f561a7ac005fb01a71f51705a13b5af06a7d8a
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ba899ea22b9dc9bf276e773e98ac84638ed5e0236de06f13d63a90b18ca9e0ec7c97d622d899796e3773930b946cd2413d098656c0c5d8cc58c6f25c21e6bd54
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
    es-errors: "npm:^1.3.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/eb3c4c4fc0381b0bf6dba2ea4d48d367c2827a0d4236a5718d97caaccc6b78f11f4cadf090736e86301d295a6aa4967ed45568f92ced51be8cbbacd9ca410943
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"asap@npm:~2.0.3, asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 10c0/0693d378cfe86842a70d4c849595a0bb50dc44c11649640ca982fa90cbfc74e3cc4753b5a0847e51933f2e9c65ce8e05576e75e5e1fd963a086e673735b35969
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axios@npm:^1.10.0":
  version: 1.10.0
  resolution: "axios@npm:1.10.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/2239cb269cc789eac22f5d1aabd58e1a83f8f364c92c2caa97b6f5cbb4ab2903d2e557d9dc670b5813e9bcdebfb149e783fb8ab3e45098635cd2f559b06bd5d8
  languageName: node
  linkType: hard

"babel-jest@npm:30.0.4, babel-jest@npm:^30.0.4":
  version: 30.0.4
  resolution: "babel-jest@npm:30.0.4"
  dependencies:
    "@jest/transform": "npm:30.0.4"
    "@types/babel__core": "npm:^7.20.5"
    babel-plugin-istanbul: "npm:^7.0.0"
    babel-preset-jest: "npm:30.0.1"
    chalk: "npm:^4.1.2"
    graceful-fs: "npm:^4.2.11"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.11.0
  checksum: 10c0/ee1df917c02e94431fa0229942609678ca255d2de97663316dd26deeaca9e9c64d4c4fc817d26d20ecab572f7aab1509d9c40803a49fb7cb0f6484fae315da07
  languageName: node
  linkType: hard

"babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": "npm:^29.7.0"
    "@types/babel__core": "npm:^7.1.14"
    babel-plugin-istanbul: "npm:^6.1.1"
    babel-preset-jest: "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 10c0/2eda9c1391e51936ca573dd1aedfee07b14c59b33dbe16ef347873ddd777bcf6e2fc739681e9e9661ab54ef84a3109a03725be2ac32cd2124c07ea4401cbe8c1
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-instrument: "npm:^5.0.4"
    test-exclude: "npm:^6.0.0"
  checksum: 10c0/1075657feb705e00fd9463b329921856d3775d9867c5054b449317d39153f8fbcebd3e02ebf00432824e647faff3683a9ca0a941325ef1afe9b3c4dd51b24beb
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^7.0.0":
  version: 7.0.0
  resolution: "babel-plugin-istanbul@npm:7.0.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-instrument: "npm:^6.0.2"
    test-exclude: "npm:^6.0.0"
  checksum: 10c0/79c37bd59ea9bcb16218e874993621e24048776fac7ee72eabe78f0909200851bdb93b32f6eba5b463206f15a1ee7ad40a725af8447952321ae1fdf14e740fe9
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:30.0.1":
  version: 30.0.1
  resolution: "babel-plugin-jest-hoist@npm:30.0.1"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    "@types/babel__core": "npm:^7.20.5"
  checksum: 10c0/49087f45c8ac359d68c622f4bd471300376b0ca2b6bd6ecaa1bd254ea87eda8fa3ce6144848e3bbabad337d276474a47e2ac3f6272f82e1f2337924ff49a02bd
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": "npm:^7.3.3"
    "@babel/types": "npm:^7.3.3"
    "@types/babel__core": "npm:^7.1.14"
    "@types/babel__traverse": "npm:^7.0.6"
  checksum: 10c0/7e6451caaf7dce33d010b8aafb970e62f1b0c0b57f4978c37b0d457bbcf0874d75a395a102daf0bae0bd14eafb9f6e9a165ee5e899c0a4f1f3bb2e07b304ed2e
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"babel-plugin-react-native-web@npm:~0.19.13":
  version: 0.19.13
  resolution: "babel-plugin-react-native-web@npm:0.19.13"
  checksum: 10c0/0710db342063182163d58febfb01ef510c9460f0500f9faaf47603d06dda37554f216e6123a099a343eb2067c2dfb43c9d4ca573a9d659662ca429048db11af4
  languageName: node
  linkType: hard

"babel-plugin-syntax-hermes-parser@npm:0.25.1, babel-plugin-syntax-hermes-parser@npm:^0.25.1":
  version: 0.25.1
  resolution: "babel-plugin-syntax-hermes-parser@npm:0.25.1"
  dependencies:
    hermes-parser: "npm:0.25.1"
  checksum: 10c0/8f4a0cb65056162b2d4c64d0ccd4d2fdeac8218e83e0338e92564ead659fd9b9351277ed2a10e958d0d8dc4c60591d5b1a40aa425bf0cbf67224e9767c557abf
  languageName: node
  linkType: hard

"babel-plugin-transform-flow-enums@npm:^0.0.2":
  version: 0.0.2
  resolution: "babel-plugin-transform-flow-enums@npm:0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow": "npm:^7.12.1"
  checksum: 10c0/aa9d022d8d4be0e7c4f1ff7e5308fe7e0ff4d6f9099449913e3a11c1e81916623a8f36432da180a9aa3f53ea534dca4401fe33d6528f043f40357cfa790ee778
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0, babel-preset-current-node-syntax@npm:^1.1.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-bigint": "npm:^7.8.3"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-import-attributes": "npm:^7.24.7"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/0b838d4412e3322cb4436f246e24e9c00bebcedfd8f00a2f51489db683bd35406bbd55a700759c28d26959c6e03f84dd6a1426f576f440267c1d7a73c5717281
  languageName: node
  linkType: hard

"babel-preset-expo@npm:~13.2.3":
  version: 13.2.3
  resolution: "babel-preset-expo@npm:13.2.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/plugin-proposal-decorators": "npm:^7.12.9"
    "@babel/plugin-proposal-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-export-default-from": "npm:^7.24.7"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.25.9"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.25.2"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.24.8"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.24.7"
    "@babel/plugin-transform-parameters": "npm:^7.24.7"
    "@babel/plugin-transform-private-methods": "npm:^7.24.7"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.24.7"
    "@babel/plugin-transform-runtime": "npm:^7.24.7"
    "@babel/preset-react": "npm:^7.22.15"
    "@babel/preset-typescript": "npm:^7.23.0"
    "@react-native/babel-preset": "npm:0.79.5"
    babel-plugin-react-native-web: "npm:~0.19.13"
    babel-plugin-syntax-hermes-parser: "npm:^0.25.1"
    babel-plugin-transform-flow-enums: "npm:^0.0.2"
    debug: "npm:^4.3.4"
    react-refresh: "npm:^0.14.2"
    resolve-from: "npm:^5.0.0"
  peerDependencies:
    babel-plugin-react-compiler: ^19.0.0-beta-e993439-20250405
  peerDependenciesMeta:
    babel-plugin-react-compiler:
      optional: true
  checksum: 10c0/403e6495f7cfc2d8d9093c4b84de9a5dfba30885fd7d547dea654f792036d8aec63c387ac0abb7155d74977c075187960c7006d9e462110c891be2a5b28d8289
  languageName: node
  linkType: hard

"babel-preset-jest@npm:30.0.1":
  version: 30.0.1
  resolution: "babel-preset-jest@npm:30.0.1"
  dependencies:
    babel-plugin-jest-hoist: "npm:30.0.1"
    babel-preset-current-node-syntax: "npm:^1.1.0"
  peerDependencies:
    "@babel/core": ^7.11.0
  checksum: 10c0/33da0094965929b1742b02e55272b544f189cd487d55bbba60e68d96d62d48f466264fe51f65950454829d4f2271541f2433e1c1c5e6a7ff5b9e91f1303471b7
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ec5fd0276b5630b05f0c14bb97cc3815c6b31600c683ebb51372e54dcb776cff790bdeeabd5b8d01ede375a040337ccbf6a3ccd68d3a34219125945e167ad943
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-js@npm:^1.2.3, base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"better-opn@npm:~3.0.2":
  version: 3.0.2
  resolution: "better-opn@npm:3.0.2"
  dependencies:
    open: "npm:^8.0.4"
  checksum: 10c0/911ef25d44da75aabfd2444ce7a4294a8000ebcac73068c04a60298b0f7c7506b60421aa4cd02ac82502fb42baaff7e4892234b51e6923eded44c5a11185f2f5
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 10c0/9604224b4c2ab3c43c075d92da15863077a9f59e5d4205f4e7e76acd0cd47e8d469ec5e5dba8d9b32aa233951893b29329ca56ac80c20ce094b4a647a66abae0
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.0":
  version: 0.1.0
  resolution: "bplist-creator@npm:0.1.0"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/86f5fe95f34abd369b381abf0f726e220ecebd60a3d932568ae94895ccf1989a87553e4aee9ab3cfb4f35e6f72319f52aa73028165eec82819ed39f15189d493
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.1":
  version: 0.1.1
  resolution: "bplist-creator@npm:0.1.1"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/427ec37263ce0e8c68a83f595fc9889a9cbf2e6fda2de18e1f8ef7f0c6ce68c0cdbb7c9c1f3bb3f2d217407af8cffbdf254bf0f71c99f2186175d07752f08a47
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.2, bplist-parser@npm:^0.3.1":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/4dc307c11d2511a01255e87e370d4ab6f1962b35fdc27605fd4ce9a557a259c2dc9f87822617ddb1f7aa062a71e30ef20d6103329ac7ce235628f637fb0ed763
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/975fecac2bb7758c062c20d0b3b6288c7cc895219ee25f0a64a9de662dbac981ff0b6e89909c3897c1f84fa353113a721923afdec5f8b2350255b097f12b1f73
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"browserslist@npm:^4.25.0":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: "npm:^1.0.30001726"
    electron-to-chromium: "npm:^1.5.173"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/acba5f0bdbd5e72dafae1e6ec79235b7bad305ed104e082ed07c34c38c7cb8ea1bc0f6be1496958c40482e40166084458fc3aee15111f15faa79212ad9081b2a
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10c0/24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.4.3":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: "npm:^2.0.0"
  checksum: 10c0/a00ca91280e10ee2321de21dda6c168e427df7a63aeaca027ea45e3e466ac5e1a5054199f6547ba1d5a513d3b6b5933457266daaa47f8857fb532a343ee6b5e1
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: "npm:^2.0.0"
  checksum: 10c0/029b5b2c557d831216305c3218e9ff30fa668be31d58dd08088f74c8eabc8362c303e0908b3a93abb25ba10e3a5bfc9cff5eb7fab6ab9cf820e3b160ccb67581
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: 10c0/13bff4fee946e6020b37e76284e95e24aa239c9e34ac4f3451e4c5330fca6f2f962e1d1ab69e4da7940e1fce135107a2b2b98c01d62ea33144350fc89dc5494e
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0, callsites@npm:^3.1.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 10c0/1a1a3137e8a781e6cbeaeab75634c60ffd8e27850de410c162cce222ea331cd1ba5364e8fb21c95e5ca76f52ac34b81a090925ca00a87221355746d049c6e273
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0, camelcase@npm:^6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001722
  resolution: "caniuse-lite@npm:1.0.30001722"
  checksum: 10c0/a1e344c392e0b138f0b215525108877d725665217a5e8e7504897e30379a5a9b858bc44799ccc0e19f4a64bf1e05c15b4a58eb1c9032293f894aa24e8d9f470f
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001726
  resolution: "caniuse-lite@npm:1.0.30001726"
  checksum: 10c0/2c5f91da7fd9ebf8c6b432818b1498ea28aca8de22b30dafabe2a2a6da1e014f10e67e14f8e68e872a0867b6b4cd6001558dde04e3ab9770c9252ca5c8849d0e
  languageName: node
  linkType: hard

"chalk@npm:^2.0.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 10c0/57a09a86371331e0be35d9083ba429e86c4f4648ecbe27455dbfb343037c16ee6fdc7f6b61f433a57cc5ded5561d71c56a150e018f40c2ffb7bc93a26dae341e
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-launcher@npm:^0.15.2":
  version: 0.15.2
  resolution: "chrome-launcher@npm:0.15.2"
  dependencies:
    "@types/node": "npm:*"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
  bin:
    print-chrome-path: bin/print-chrome-path.js
  checksum: 10c0/fc01abc19af753bb089744362c0de48707f32ea15779407b06fb569e029a6b1fbaa78107165539d768915cf54b5c38594e73d95563c34127873e3826fb43c636
  languageName: node
  linkType: hard

"chromium-edge-launcher@npm:^0.2.0":
  version: 0.2.0
  resolution: "chromium-edge-launcher@npm:0.2.0"
  dependencies:
    "@types/node": "npm:*"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
    mkdirp: "npm:^1.0.4"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/880972816dd9b95c0eb77d1f707569667a8cce7cc29fe9c8d199c47fdfbe4971e9da3e5a29f61c4ecec29437ac7cebbbb5afc30bec96306579d1121e7340606a
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 10c0/8c5fa3830a2bcee2b53c2e5018226f0141db9ec9f7b1e27a5c57db5512332cde8a0beb769bcbaf0d8775a78afbf2bb841928feca4ea6219638a5b088f9884b46
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0, ci-info@npm:^3.3.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10c0/6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"ci-info@npm:^4.2.0":
  version: 4.3.0
  resolution: "ci-info@npm:4.3.0"
  checksum: 10c0/60d3dfe95d75c01454ec1cfd5108617dd598a28a2a3e148bd7e1523c1c208b5f5a3007cafcbe293e6fd0a5a310cc32217c5dc54743eeabc0a2bec80072fc055c
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^2.1.0":
  version: 2.1.0
  resolution: "cjs-module-lexer@npm:2.1.0"
  checksum: 10c0/91cf28686dc3948e4a06dfa03a2fccb14b7a97471ffe7ae0124f62060ddf2de28e8e997f60007babe6e122b1b06a47c01a1b72cc015f185824d9cac3ccfa5533
  languageName: node
  linkType: hard

"class-variance-authority@npm:^0.7.1":
  version: 0.7.1
  resolution: "class-variance-authority@npm:0.7.1"
  dependencies:
    clsx: "npm:^2.1.1"
  checksum: 10c0/0f438cea22131808b99272de0fa933c2532d5659773bfec0c583de7b3f038378996d3350683426b8e9c74a6286699382106d71fbec52f0dd5fbb191792cccb5b
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: "npm:^2.0.0"
  checksum: 10c0/09ee6d8b5b818d840bf80ec9561eaf696672197d3a02a7daee2def96d5f52ce6e0bbe7afca754ccf14f04830b5a1b4556273e983507d5029f95bba3016618eda
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"client-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 10c0/9d6cfd0c19e1c96a434605added99dff48482152af791ec4172fb912a71cff9027ff174efd8cdb2160cc7f377543e0537ffc462d4f279bc4701de3f2a3c4b358
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/6035f5daf7383470cef82b3d3db00bec70afb3423538c50394386ffbbab135e26c3689c41791f911fa71b62d13d3863c712fdd70f0fbdffd938a1e6fd09aac00
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 10c0/c0e85ea0ca8bf0a50cbdca82efc5af0301240ca88ebe3644a6ffb8ffe911f34d40f8fbcf8f1d52c5ddd66706abd4d3bfcd64259f1e8e2371d4f47573b0dc8c28
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.2":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: 10c0/ed7008e2e8b6852c5483b444a3ae6e976e088d4335a85aa0a9db2861c5f1d31bd2d7ff97a60469b3388deeba661a619753afbe201279fb159b4b9548ab8269a1
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0, color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0, color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^3.1.2":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.3"
    color-string: "npm:^1.6.0"
  checksum: 10c0/39345d55825884c32a88b95127d417a2c24681d8b57069413596d9fcbb721459ef9d9ec24ce3e65527b5373ce171b73e38dbcd9c830a52a6487e7f37bf00e83c
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^12.0.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10c0/6e1996680c083b3b897bfc1cfe1c58dfbcd9842fd43e1aaf8a795fbc237f65efcc860a3ef457b318e73f29a4f4a28f6403c3d653d021d960e4632dd45bde54a9
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 10c0/84a76c08fe6cc08c9c93f62ac573d2907d8e79138999312c92d4155bc2325d487d64d13f669b2000c9f8caf70493c1be2dac74fec3c51d5a04f8bc3ae1830bab
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"comment-json@npm:^4.2.5":
  version: 4.2.5
  resolution: "comment-json@npm:4.2.5"
  dependencies:
    array-timsort: "npm:^1.0.3"
    core-util-is: "npm:^1.0.3"
    esprima: "npm:^4.0.1"
    has-own-prop: "npm:^2.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10c0/e22f13f18fcc484ac33c8bc02a3d69c3f9467ae5063fdfb3df7735f83a8d9a2cab6a32b7d4a0c53123413a9577de8e17c8cc88369c433326799558febb34ef9c
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/804d3c8430939f4fd88e5128333f311b4035f6425a7f2959d74cfb5c98ef3a3e3e18143208f3f9d0fcae4cd3bcf3d2fbe525e0fcb955e6e146e070936f025a24
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"connect@npm:^3.6.5, connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: "npm:2.6.9"
    finalhandler: "npm:1.1.2"
    parseurl: "npm:~1.3.3"
    utils-merge: "npm:1.0.1"
  checksum: 10c0/f120c6116bb16a0a7d2703c0b4a0cd7ed787dc5ec91978097bf62aa967289020a9f41a9cd3c3276a7b92aaa36f382d2cd35fed7138fd466a55c8e9fdbed11ca8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: "npm:^4.25.0"
  checksum: 10c0/923804c16faf91bacb747a697640a907cb2a3e63078d467a75eb7ea4187d62d36347a94e5826d1b36739012e81a2ea435922cc8bd8e228fa68efaf00a9ce94af
  languageName: node
  linkType: hard

"core-util-is@npm:^1.0.3":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.5":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: "npm:^2.0.0"
    is-directory: "npm:^0.3.1"
    js-yaml: "npm:^3.13.1"
    parse-json: "npm:^4.0.0"
  checksum: 10c0/ae9ba309cdbb42d0c9d63dad5c1dfa1c56bb8f818cb8633eea14fd2dbdc9f33393b77658ba96fdabda497bc943afed8c3371d1222afe613c518ba676fa624645
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: "npm:^2.7.0"
  checksum: 10c0/d8596adf0269130098a676f6739a0922f3cc7b71cc89729925411ebe851a87026171c82ea89154c4811c9867c01c44793205a52e618ce2684650218c7fbeeb9f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 10c0/288589b2484fe787f9e146f56c4be90b940018f17af1b152e4dde12309042ff5a2bf69e949aab8b8ac253948381529cc6f3e5a2427b73643a71ff177fa122b37
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: "npm:^1.0.3"
  checksum: 10c0/8bb042e8f7701a7edadc3cce5ce2d5cf41189631d7e2aed194d5a7059b25776dded2a0466cb9da1d1f3fc6c99dcecb51e45671148d073b8a2a71e34755152e52
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: "npm:2.0.14"
    source-map: "npm:^0.6.1"
  checksum: 10c0/499a507bfa39b8b2128f49736882c0dd636b0cd3370f2c69f4558ec86d269113286b7df469afc955de6a68b0dba00bc533e40022a73698081d600072d5d83c1c
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10c0/6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.3.7, debug@npm:^4.4.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:^3.1.0, debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"dedent@npm:^1.6.0":
  version: 1.6.0
  resolution: "dedent@npm:1.6.0"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 10c0/671b8f5e390dd2a560862c4511dd6d2638e71911486f78cb32116551f8f2aa6fcaf50579ffffb2f866d46b5b80fd72470659ca5760ede8f967619ef7df79e8a5
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deepmerge@npm:^3.2.0":
  version: 3.3.0
  resolution: "deepmerge@npm:3.3.0"
  checksum: 10c0/143bc6b6cd8a1216565c61c0fe38bf43fe691fb6876fb3f5727c6e323defe4e947c68fbab9957e17e837c5594a56af885c5834d23dc6cf2c41bef97090005104
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"detect-newline@npm:^3.1.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: 10c0/c38cfc8eeb9fda09febb44bcd85e467c970d4e3bf526095394e5a4f18bc26dd0cf6b22c69c1fa9969261521c593836db335c2795218f6d781a512aea2fb8209d
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: 10c0/95d0b53d23b851aacff56dfadb7ecfedce49da4232233baecfeecb7710248c4aa03f0aa8995062f0acafaf925adf8536bd7044a2e68316fd7d411477599bc27b
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: 10c0/32e27ac7dbffdf2fb0eb5a84efd98a9ad084fbabd5ac9abb8757c6770d5320d2acd172830b28c4add29bb873d59420601dfc805ac4064330ce59b1adfd0593b2
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: 10c0/03eb4e769f19a027fd5b43b59e8a05e3fd2100ac239ebb0bf9a745de35d449e2f25cfaf3aa3934664551d72856f4ae8b7822016ce5c42c2d27c18ae79429ec42
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/b6416aaff1f380bf56c3b552f31fdf7a69b45689368deca72d28636f41c16bb28ec3ebc40ace97db4c1afc0ceeb8120e8492fe0046841c94c2933b2e30a7d5ac
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"dotenv-expand@npm:~11.0.6":
  version: 11.0.7
  resolution: "dotenv-expand@npm:11.0.7"
  dependencies:
    dotenv: "npm:^16.4.5"
  checksum: 10c0/d80b8a7be085edf351270b96ac0e794bc3ddd7f36157912939577cb4d33ba6492ebee349d59798b71b90e36f498d24a2a564fb4aa00073b2ef4c2a3a49c467b1
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10c0/5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dotenv@npm:^17.2.0":
  version: 17.2.0
  resolution: "dotenv@npm:17.2.0"
  checksum: 10c0/d4cb5c2a09c288c921ef47a78f51c765a763266e23e84465cde6a87b28b9f7fc5a9d1fe349ed3c880b4c7297f82dd736dba5b08fe95ce53833ce4a7258d0fc43
  languageName: node
  linkType: hard

"dotenv@npm:~16.4.5":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: 10c0/be9f597e36a8daf834452daa1f4cc30e5375a5968f98f46d89b16b983c567398a330580c88395069a77473943c06b877d1ca25b4afafcdd6d4adb549e8293462
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.166
  resolution: "electron-to-chromium@npm:1.5.166"
  checksum: 10c0/0244c09799f492035af63bb87857561aa034670a742cd80a78de5a88a0d536b0945fb078a636777d064d2451401c5d8302dfa8da7c996afe7476bf277b2dea63
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.176
  resolution: "electron-to-chromium@npm:1.5.176"
  checksum: 10c0/ab591218086d6f73d2f773756f7d277a1d176a6b83d6499864d2bc305b1ea1c7673c30f8541d0c55d40fb7f2595fe4041e72aa07351275ba04862b9bc507cc66
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10c0/1573d0ae29ab34661b6c63251ff8f5facd24ccf6a823f19417ae8ba8c88ea450325788c67f16c99edec8de4b52ce93a10fe441ece389fd156e88ee7dab9bfa35
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"entities@npm:^4.2.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"env-editor@npm:^0.4.1":
  version: 0.4.2
  resolution: "env-editor@npm:0.4.2"
  checksum: 10c0/edb33583b0ae5197535905cbcefca424796f6afec799604f7578428ee523245edcd7df48d582fdab67dbcc697ed39070057f512e72f94c91ceefdcb432f5eadb
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-negative-zero: "npm:^2.0.3"
    is-regex: "npm:^1.2.1"
    is-set: "npm:^2.0.3"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    stop-iteration-iterator: "npm:^1.1.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10c0/b256e897be32df5d382786ce8cce29a1dd8c97efbab77a26609bd70f2ed29fbcfc7a31758cb07488d532e7ccccdfca76c1118f2afe5a424cdc05ca007867c318
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-set-tostringtag: "npm:^2.0.3"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.6"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    iterator.prototype: "npm:^1.1.4"
    safe-array-concat: "npm:^1.1.3"
  checksum: 10c0/97e3125ca472d82d8aceea11b790397648b52c26d8768ea1c1ee6309ef45a8755bb63225a43f3150c7591cffc17caf5752459f1e70d583b4184370a8f04ebd2f
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/1b9702c8a1823fc3ef39035a4e958802cf294dd21e917397c561d0b3e195f383b978359816b1732d02b255ccf63e1e4815da0065b95db8d7c992037be3bbbcdb
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10c0/2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-expo@npm:~9.2.0":
  version: 9.2.0
  resolution: "eslint-config-expo@npm:9.2.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:^8.18.2"
    "@typescript-eslint/parser": "npm:^8.18.2"
    eslint-import-resolver-typescript: "npm:^3.6.3"
    eslint-plugin-expo: "npm:^0.1.4"
    eslint-plugin-import: "npm:^2.30.0"
    eslint-plugin-react: "npm:^7.37.3"
    eslint-plugin-react-hooks: "npm:^5.1.0"
    globals: "npm:^16.0.0"
  peerDependencies:
    eslint: ">=8.10"
  checksum: 10c0/de9ce38927038f6b2baf9e3e1f68bd0e7db3950e5fbf90f8ee45a1278da299758e269672e8e5d488866c4eb5ef85017134912da1e60c79a85bc30b7b107474f0
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^10.1.3":
  version: 10.1.5
  resolution: "eslint-config-prettier@npm:10.1.5"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/5486255428e4577e8064b40f27db299faf7312b8e43d7b4bc913a6426e6c0f5950cd519cad81ae24e9aecb4002c502bc665c02e3b52efde57af2debcf27dd6e0
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: 10c0/0ea8a24a72328a51fd95aa8f660dcca74c1429806737cf10261ab90cfcaaf62fd1eff664b76a44270868e0a932711a81b250053942595bcd00a93b1c1575dd61
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.6.3":
  version: 3.10.1
  resolution: "eslint-import-resolver-typescript@npm:3.10.1"
  dependencies:
    "@nolyfill/is-core-module": "npm:1.0.39"
    debug: "npm:^4.4.0"
    get-tsconfig: "npm:^4.10.0"
    is-bun-module: "npm:^2.0.0"
    stable-hash: "npm:^0.0.5"
    tinyglobby: "npm:^0.2.13"
    unrs-resolver: "npm:^1.6.2"
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: 10c0/02ba72cf757753ab9250806c066d09082e00807b7b6525d7687e1c0710bc3f6947e39120227fe1f93dabea3510776d86fb3fd769466ba3c46ce67e9f874cb702
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10c0/4d8b46dcd525d71276f9be9ffac1d2be61c9d54cc53c992e6333cf957840dee09381842b1acbbb15fc6b255ebab99cd481c5007ab438e5455a14abe1a0468558
  languageName: node
  linkType: hard

"eslint-plugin-expo@npm:^0.1.4":
  version: 0.1.4
  resolution: "eslint-plugin-expo@npm:0.1.4"
  dependencies:
    "@typescript-eslint/types": "npm:^8.29.1"
    "@typescript-eslint/utils": "npm:^8.29.1"
    eslint: "npm:^9.24.0"
  peerDependencies:
    eslint: ">=8.10"
  checksum: 10c0/b7d57edd64f96d32b5df91da88b55443e8af0f866a09a450f3f92cb245a08bb3d4506ea4b7820b072d6a9c2a92ed9a9877f9b5faf4660e242b17758211cbe8fc
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.30.0":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": "npm:^1.1.0"
    array-includes: "npm:^3.1.8"
    array.prototype.findlastindex: "npm:^1.2.5"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.12.0"
    hasown: "npm:^2.0.2"
    is-core-module: "npm:^2.15.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    object.groupby: "npm:^1.0.3"
    object.values: "npm:^1.2.0"
    semver: "npm:^6.3.1"
    string.prototype.trimend: "npm:^1.0.8"
    tsconfig-paths: "npm:^3.15.0"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 10c0/e21d116ddd1900e091ad120b3eb68c5dd5437fe2c930f1211781cd38b246f090a6b74d5f3800b8255a0ed29782591521ad44eb21c5534960a8f1fb4040fd913a
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.4.0":
  version: 5.4.1
  resolution: "eslint-plugin-prettier@npm:5.4.1"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.11.7"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: ">= 7.0.0 <10.0.0 || >=10.1.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10c0/bdd9e9473bf3f995521558eb5e2ee70dd4f06cb8b9a6192523cfed76511924fad31ec9af9807cd99f693dc59085e0a1db8a1d3ccc283e98ab30eb32cc7469649
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.1.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10c0/1c8d50fa5984c6dea32470651807d2922cc3934cf3425e78f84a24c2dfd972e7f019bee84aefb27e0cf2c13fea0ac1d4473267727408feeb1c56333ca1489385
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.37.3":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: "npm:^3.1.8"
    array.prototype.findlast: "npm:^1.2.5"
    array.prototype.flatmap: "npm:^1.3.3"
    array.prototype.tosorted: "npm:^1.1.4"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.2.1"
    estraverse: "npm:^5.3.0"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.9"
    object.fromentries: "npm:^2.0.8"
    object.values: "npm:^1.2.1"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.5"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.12"
    string.prototype.repeat: "npm:^1.0.0"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 10c0/c850bfd556291d4d9234f5ca38db1436924a1013627c8ab1853f77cac73ec19b020e861e6c7b783436a48b6ffcdfba4547598235a37ad4611b6739f65fd8ad57
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.3.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/407f6c600204d0f3705bd557f81bd0189e69cd7996f408f8971ab5779c0af733d1af2f1412066b40ee1588b085874fc37a2333986c6521669cdbdd36ca5058e0
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0, eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 10c0/fcd43999199d6740db26c58dbe0c2594623e31ca307e616ac05153c9272f12f1364f5a0b1917a8e962268fdecc6f3622c1c2908b4fcc2e047a106fe6de69dc43
  languageName: node
  linkType: hard

"eslint@npm:^9.0.0, eslint@npm:^9.24.0":
  version: 9.28.0
  resolution: "eslint@npm:9.28.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.1"
    "@eslint/core": "npm:^0.14.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.28.0"
    "@eslint/plugin-kit": "npm:^0.3.1"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/513ea7e69d88a0905d4ed35cef3a8f31ebce7ca9f2cdbda3474495c63ad6831d52357aad65094be7a144d6e51850980ced7d25efb807e8ab06a427241f7cd730
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: "npm:^8.15.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10c0/c63fe06131c26c8157b4083313cb02a9a54720a08e21543300e55288c40e06c3fc284bdecf108d3a1372c5934a0a88644c98714f38b6ae8ed272b40d9ea08d6b
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0, event-target-shim@npm:^5.0.1":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"exec-async@npm:^2.2.0":
  version: 2.2.0
  resolution: "exec-async@npm:2.2.0"
  checksum: 10c0/9c70693a3d9f53e19cc8ecf26c3b3fc7125bf40051a71cba70d71161d065a6091d3ab1924c56ac1edd68cb98b9fbef29f83e45dcf67ee6b6c4826e0f898ac039
  languageName: node
  linkType: hard

"execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exit-x@npm:^0.2.2":
  version: 0.2.2
  resolution: "exit-x@npm:0.2.2"
  checksum: 10c0/212a7a095ca5540e9581f1ef2d1d6a40df7a6027c8cc96e78ce1d16b86d1a88326d4a0eff8dff2b5ec1e68bb0c1edd5d0dfdde87df1869bf7514d4bc6a5cbd72
  languageName: node
  linkType: hard

"expect@npm:30.0.4, expect@npm:^30.0.0":
  version: 30.0.4
  resolution: "expect@npm:30.0.4"
  dependencies:
    "@jest/expect-utils": "npm:30.0.4"
    "@jest/get-type": "npm:30.0.1"
    jest-matcher-utils: "npm:30.0.4"
    jest-message-util: "npm:30.0.2"
    jest-mock: "npm:30.0.2"
    jest-util: "npm:30.0.2"
  checksum: 10c0/de0c7cf4068591feda6b4b1dfcb5711f085266bfa720a8498ac8c0d03fbfa84881f54b67f25c79bee4bf0f6040ee12ed004b209de7d0cff82fd06d7b42baabc2
  languageName: node
  linkType: hard

"expo-application@npm:^6.1.5":
  version: 6.1.5
  resolution: "expo-application@npm:6.1.5"
  peerDependencies:
    expo: "*"
  checksum: 10c0/c4fa0bddfc911af17055334558314d819d403efa5db22b05cffc44c91eef38e9fb57b4a5aae35378523c59847189a7ca09ad9e5370ee5a3b0f23c1c5146c8683
  languageName: node
  linkType: hard

"expo-asset@npm:~11.1.7":
  version: 11.1.7
  resolution: "expo-asset@npm:11.1.7"
  dependencies:
    "@expo/image-utils": "npm:^0.7.6"
    expo-constants: "npm:~17.1.7"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/32d1ccd630c7e62ec4b71cf03e2414fb389ca5d6383a5243ff9efe6ac71061ea626aa7f1503ccd3187d62451165034265b2ed04e508626710f810c0d0246a81d
  languageName: node
  linkType: hard

"expo-audio@npm:^0.4.8":
  version: 0.4.8
  resolution: "expo-audio@npm:0.4.8"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/23c16252856a7f32b3b707ee566ec2b0c9fb0324f2bf785e752174b8104b5850ce16de3f94723b6c84c2b86dfd3ae5e98aa885c4d2ac67d59382e13560ae325c
  languageName: node
  linkType: hard

"expo-blur@npm:^14.1.5":
  version: 14.1.5
  resolution: "expo-blur@npm:14.1.5"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/4ffe9014219f23d53f66bc61ea5d00b74a012d120f94a810c71cbdd34901900282a40f7d0636877cd261bc7ff8a3db20da2e0be9393b1a950b8fc677e285bf65
  languageName: node
  linkType: hard

"expo-constants@npm:~17.1.7":
  version: 17.1.7
  resolution: "expo-constants@npm:17.1.7"
  dependencies:
    "@expo/config": "npm:~11.0.12"
    "@expo/env": "npm:~1.0.7"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/84ef3b9de11aa7b55cf5867213b7211bcee982eda0a630a657e22671be2d85bed7f3f092acdecc5bb6bc940c611212657532a6e53f62b384414988d988e96a26
  languageName: node
  linkType: hard

"expo-device@npm:^7.1.4":
  version: 7.1.4
  resolution: "expo-device@npm:7.1.4"
  dependencies:
    ua-parser-js: "npm:^0.7.33"
  peerDependencies:
    expo: "*"
  checksum: 10c0/e31c952251b36d698185d1b2c1799d4626a863896e650d31232d9a47aacc1387e01b5f33b5b2b4fe388e81a9287e2c486157644479d1bbf580e8050818aaff81
  languageName: node
  linkType: hard

"expo-file-system@npm:~18.1.11":
  version: 18.1.11
  resolution: "expo-file-system@npm:18.1.11"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/e7c4c02acfbb7c5b6f8afff7e010af28ca7112c2ab0e7baddab394b275edc363d0587aedbb1996058a65e2d1e8aba0074b49007cad37d10a4b5ef2987b7f6bdf
  languageName: node
  linkType: hard

"expo-font@npm:^13.3.2, expo-font@npm:~13.3.2":
  version: 13.3.2
  resolution: "expo-font@npm:13.3.2"
  dependencies:
    fontfaceobserver: "npm:^2.1.0"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: 10c0/00711fbdb380f076594b43caddf06ad6f70a5261caaccce564dabf01a02054f89987ac0367c96ad8cb38b4edbf904e99839ab2a446d7b57e7e84b29145da4ccd
  languageName: node
  linkType: hard

"expo-image@npm:2.3.2":
  version: 2.3.2
  resolution: "expo-image@npm:2.3.2"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native-web:
      optional: true
  checksum: 10c0/afd0bede1e2a8f36a12ccb87121c3a35af543e9e95ed9d1dce60b06858499b5b5f31e6315347dc494e3587aa5f9c70ade7c9d53582f92d732a5490bd79852ed5
  languageName: node
  linkType: hard

"expo-keep-awake@npm:~14.1.4":
  version: 14.1.4
  resolution: "expo-keep-awake@npm:14.1.4"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: 10c0/9d1993f7b17e6c36d707501ba9d983fe7e640317af7f2ecee17255f2c8c7bae39bdb3dfffcc67afcd36457bb60a6799dbaa2414922507ef01298cbe34e8eae8e
  languageName: node
  linkType: hard

"expo-linear-gradient@npm:~14.1.5":
  version: 14.1.5
  resolution: "expo-linear-gradient@npm:14.1.5"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/14484683a36d8346ee2339530edb4927bbdb58833728ac34cdfe2df38541705cd9ef5f460c89dd28617af760b12197870bc411c48780f57bf1ba969bf54b13a0
  languageName: node
  linkType: hard

"expo-linking@npm:~7.1.7":
  version: 7.1.7
  resolution: "expo-linking@npm:7.1.7"
  dependencies:
    expo-constants: "npm:~17.1.7"
    invariant: "npm:^2.2.4"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/4b4ec130d768beb4cecf85a33578e3eca1a69428a212ab7470ea1b1b7ff370607576832efbd7c3b5c047fb6d034a577aced42fd588cbeb46782e22ce2851542e
  languageName: node
  linkType: hard

"expo-modules-autolinking@npm:2.1.14":
  version: 2.1.14
  resolution: "expo-modules-autolinking@npm:2.1.14"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.1.0"
    commander: "npm:^7.2.0"
    find-up: "npm:^5.0.0"
    glob: "npm:^10.4.2"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
  bin:
    expo-modules-autolinking: bin/expo-modules-autolinking.js
  checksum: 10c0/3d416a5ca69c95f462f6aa138ebc5ef6ea4f57e668f773235576f39f21285cb78c9a9b6b499603ec578903922f4e1c6aef62f3cc3156a1525f4af863cd3c3532
  languageName: node
  linkType: hard

"expo-modules-core@npm:2.4.2":
  version: 2.4.2
  resolution: "expo-modules-core@npm:2.4.2"
  dependencies:
    invariant: "npm:^2.2.4"
  checksum: 10c0/4f44514ef8a0f673c6af988c92bed35d410b5725b989e2b91210b3e70d9e56dccb51ca439740997f95d7dc2f9fd7d2ff53cbd37396546986c0369587c779be73
  languageName: node
  linkType: hard

"expo-navigation-bar@npm:4.2.7":
  version: 4.2.7
  resolution: "expo-navigation-bar@npm:4.2.7"
  dependencies:
    "@react-native/normalize-colors": "npm:0.79.5"
    debug: "npm:^4.3.2"
    react-native-edge-to-edge: "npm:1.6.0"
    react-native-is-edge-to-edge: "npm:^1.1.6"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/7b92ad31ea90fbfffdd38042864e1e1bc4dccea62b82b8c5b806b7899c1ae3ce06c3bc9e234fd3624cdce4d5f9244bb504b95df8cc53e4e3a40514d4a634850b
  languageName: node
  linkType: hard

"expo-router@npm:~5.1.3":
  version: 5.1.3
  resolution: "expo-router@npm:5.1.3"
  dependencies:
    "@expo/metro-runtime": "npm:5.0.4"
    "@expo/server": "npm:^0.6.3"
    "@radix-ui/react-slot": "npm:1.2.0"
    "@react-navigation/bottom-tabs": "npm:^7.3.10"
    "@react-navigation/native": "npm:^7.1.6"
    "@react-navigation/native-stack": "npm:^7.3.10"
    client-only: "npm:^0.0.1"
    invariant: "npm:^2.2.4"
    react-fast-compare: "npm:^3.2.2"
    react-native-is-edge-to-edge: "npm:^1.1.6"
    schema-utils: "npm:^4.0.1"
    semver: "npm:~7.6.3"
    server-only: "npm:^0.0.1"
    shallowequal: "npm:^1.1.0"
  peerDependencies:
    "@react-navigation/drawer": ^7.3.9
    expo: "*"
    expo-constants: "*"
    expo-linking: "*"
    react-native-reanimated: "*"
    react-native-safe-area-context: "*"
    react-native-screens: "*"
  peerDependenciesMeta:
    "@react-navigation/drawer":
      optional: true
    "@testing-library/jest-native":
      optional: true
    react-native-reanimated:
      optional: true
  checksum: 10c0/30bef85d510719c6baf8c7641b0787153751d11c7100a622935cbb24475aad30979709567dec7f4d63b9be6572017d4031c72fd25b32e413d40dacbd71867803
  languageName: node
  linkType: hard

"expo-screen-capture@npm:^7.1.5":
  version: 7.1.5
  resolution: "expo-screen-capture@npm:7.1.5"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: 10c0/532dcb1f6c63ad445452581271e74ae1b179cc2c914371f74cc7b1f4243f5f593303a4a44f642ed8aa4db7411b31db928d1a2f6b272929fe8e5d90b2721260cc
  languageName: node
  linkType: hard

"expo-splash-screen@npm:~0.30.10":
  version: 0.30.10
  resolution: "expo-splash-screen@npm:0.30.10"
  dependencies:
    "@expo/prebuild-config": "npm:^9.0.10"
  peerDependencies:
    expo: "*"
  checksum: 10c0/27cac5971711a84bd81bb001e6f19791fb2ac457d8f9e5e207fb93e41fc020e238ef675b64ea82b0e40d3df70f20b44133bce7912a02bb883468278ac252015a
  languageName: node
  linkType: hard

"expo-status-bar@npm:~2.2.3":
  version: 2.2.3
  resolution: "expo-status-bar@npm:2.2.3"
  dependencies:
    react-native-edge-to-edge: "npm:1.6.0"
    react-native-is-edge-to-edge: "npm:^1.1.6"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/8deee621bd94336c9f9bab500f199f6ec5320eaf448c3e4b26e762cf0d0ad34d08ccae9753124c7e966c92cb370eb7440a0a8afba33e904f4a9964e5da1346d0
  languageName: node
  linkType: hard

"expo-video@npm:^2.2.2":
  version: 2.2.2
  resolution: "expo-video@npm:2.2.2"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/d7b4060748b69750c0afa4300ac8830ff90fe34057dbe9efcdb3a31d7c73d3b3f88087a3ae546e4da497577446a00d0602f7521e6a3150ba8998d94a0e9585ff
  languageName: node
  linkType: hard

"expo@npm:53.0.19":
  version: 53.0.19
  resolution: "expo@npm:53.0.19"
  dependencies:
    "@babel/runtime": "npm:^7.20.0"
    "@expo/cli": "npm:0.24.20"
    "@expo/config": "npm:~11.0.13"
    "@expo/config-plugins": "npm:~10.1.2"
    "@expo/fingerprint": "npm:0.13.4"
    "@expo/metro-config": "npm:0.20.17"
    "@expo/vector-icons": "npm:^14.0.0"
    babel-preset-expo: "npm:~13.2.3"
    expo-asset: "npm:~11.1.7"
    expo-constants: "npm:~17.1.7"
    expo-file-system: "npm:~18.1.11"
    expo-font: "npm:~13.3.2"
    expo-keep-awake: "npm:~14.1.4"
    expo-modules-autolinking: "npm:2.1.14"
    expo-modules-core: "npm:2.4.2"
    react-native-edge-to-edge: "npm:1.6.0"
    whatwg-url-without-unicode: "npm:8.0.0-3"
  peerDependencies:
    "@expo/dom-webview": "*"
    "@expo/metro-runtime": "*"
    react: "*"
    react-native: "*"
    react-native-webview: "*"
  peerDependenciesMeta:
    "@expo/dom-webview":
      optional: true
    "@expo/metro-runtime":
      optional: true
    react-native-webview:
      optional: true
  bin:
    expo: bin/cli
    expo-modules-autolinking: bin/autolinking
    fingerprint: bin/fingerprint
  checksum: 10c0/7fb7a1ae7309dd6c18b410109b1e7b8e38409d59045b0237d93392fa5c1aaf5a71fa246e157402b3af73e672f259f0348308bf378d873d40a2b11cba05e006fa
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10c0/5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10c0/74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0, fb-watchman@npm:^2.0.2":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10c0/feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 10c0/dfb64116b125a64abecca9e31477b5edb9a2332c5ffe74326fe36e0a72eef7fc8a49b86adf36c2c293078d79f4524f35e80f5e62546395f53fb7c9e69821f54f
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.4":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: "npm:^3.1.5"
    fbjs-css-vars: "npm:^1.0.0"
    loose-envify: "npm:^1.0.0"
    object-assign: "npm:^4.1.0"
    promise: "npm:^7.1.1"
    setimmediate: "npm:^1.0.5"
    ua-parser-js: "npm:^1.0.35"
  checksum: 10c0/66d0a2fc9a774f9066e35ac2ac4bf1245931d27f3ac287c7d47e6aa1fc152b243c2109743eb8f65341e025621fb51a12038fadb9fd8fda2e3ddae04ebab06f91
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10c0/071e0886b2b50238ca5026c5bbf58c26a7c1a1f720773b8c7813d16ba93d0200de977af14ac143c5ac18f666b2cfc83073f3a5fe6a4e996c49e0863d5500fccf
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/6a96e1f5caab085628c11d9fdceb82ba608d5e426c6913d4d918409baa271037a47f28fbba73279e8ad614f0b8fa71ea791d265e408d760793829edd8c2f4584
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flow-enums-runtime@npm:^0.0.6":
  version: 0.0.6
  resolution: "flow-enums-runtime@npm:0.0.6"
  checksum: 10c0/f0b9ca52dbf9cf30264ebf1af034ac7b80fb5e5ef009efc789b89a90aa17349a3ff5672b3b27c6eb89d5e02808fc0dfb7effbfc5a793451694d6cce48774d51e
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"fontfaceobserver@npm:^2.1.0":
  version: 2.3.0
  resolution: "fontfaceobserver@npm:2.3.0"
  checksum: 10c0/9b539d5021757d3ed73c355bdb839296d6654de473a992aa98993ef46d951f0361545323de68f6d70c5334d7e3e9f409c1ae7a03c168b00cb0f6c5dea6c77bfa
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/f0cf45873d600110b5fadf5804478377694f73a1ed97aaa370a74c90cebd7fe6e845a081171668a5476477d0d55a73a4e03d6682968fa8661eac2a81d651fcdb
  languageName: node
  linkType: hard

"freeport-async@npm:^2.0.0":
  version: 2.0.0
  resolution: "freeport-async@npm:2.0.0"
  checksum: 10c0/421828d1a689695b6c8122d310fd8941af99ebe0b5793e3f8d49aa5923ce580b6c4dd6b7470d46983e60839c302f6c793a8541dbab80817396cdde2b04c83c90
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:^2.3.3, fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A^2.3.3#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10c0/7f8e3dabc6a49b747920a800fb88e1952fef871cdf51b79e98db48275a5de6cdaf499c55ee67df5fa6fe7ce65f0063e26de0f2e53049b408c585aa74d39ffa21
  languageName: node
  linkType: hard

"getenv@npm:^2.0.0":
  version: 2.0.0
  resolution: "getenv@npm:2.0.0"
  checksum: 10c0/397ff641dd70cd78e414430258651e9a2228d3c5553a8cf15ae7840f75d3f10dfcb83f668f84829e84ea665b0fce2f08a9eddda3c9dcd7faa2d3da1c182c1854
  languageName: node
  linkType: hard

"gifted-charts-core@npm:0.1.63":
  version: 0.1.63
  resolution: "gifted-charts-core@npm:0.1.63"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-svg: "*"
  checksum: 10c0/e1f77624ed48e4ce1755d71015344242d6314f5dd8c2af78462324d133deb815aef23bfd4f76bb114977c9dd3930d430c0b5e81bd15c6f5fe8be158ec8e52888
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.4.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.1, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globals@npm:^16.0.0":
  version: 16.2.0
  resolution: "globals@npm:16.2.0"
  checksum: 10c0/c2b3ea163faa6f8a38076b471b12f4bda891f7df7f7d2e8294fb4801d735a51a73431bf4c1696c5bf5dbca5e0a0db894698acfcbd3068730c6b12eef185dea25
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-own-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-own-prop@npm:2.0.0"
  checksum: 10c0/2745497283d80228b5c5fbb8c63ab1029e604bce7db8d4b36255e427b3695b2153dc978b176674d0dd2a23f132809e04d7ef41fefc0ab85870a5caa918c5c0d9
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hermes-estree@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-estree@npm:0.25.1"
  checksum: 10c0/48be3b2fa37a0cbc77a112a89096fa212f25d06de92781b163d67853d210a8a5c3784fac23d7d48335058f7ed283115c87b4332c2a2abaaccc76d0ead1a282ac
  languageName: node
  linkType: hard

"hermes-estree@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-estree@npm:0.28.1"
  checksum: 10c0/aa00f437c82099b9043e384b529c75de21d0111b792ab7480fe992975b5f9535a8581664789db197824a7825ea66d2fd70eb20cb568c5315804421deaf009500
  languageName: node
  linkType: hard

"hermes-parser@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-parser@npm:0.25.1"
  dependencies:
    hermes-estree: "npm:0.25.1"
  checksum: 10c0/3abaa4c6f1bcc25273f267297a89a4904963ea29af19b8e4f6eabe04f1c2c7e9abd7bfc4730ddb1d58f2ea04b6fee74053d8bddb5656ec6ebf6c79cc8d14202c
  languageName: node
  linkType: hard

"hermes-parser@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-parser@npm:0.28.1"
  dependencies:
    hermes-estree: "npm:0.28.1"
  checksum: 10c0/c6d3c01fb1ea5232f4587b6b038f5c2c6414932e7c48efbe156ab160e2bcaac818c9eb2f828f30967a24b40f543cad503baed0eedf5a7e877852ed271915981f
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.1":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10c0/fe0889169e845d738b59b64badf5e55fa3cf20454f9203d1eb088df322d49d4318df774828e789898dcb280e8a5521bb59b3203385662ca5e9218a6ca5820e74
  languageName: node
  linkType: hard

"hosted-git-info@npm:^7.0.0":
  version: 7.0.2
  resolution: "hosted-git-info@npm:7.0.2"
  dependencies:
    lru-cache: "npm:^10.0.1"
  checksum: 10c0/b19dbd92d3c0b4b0f1513cf79b0fc189f54d6af2129eeb201de2e9baaa711f1936929c848b866d9c8667a0f956f34bf4f07418c12be1ee9ca74fd9246335ca1f
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.5":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.1.0
  resolution: "hyphenate-style-name@npm:1.1.0"
  checksum: 10c0/bfe88deac2414a41a0d08811e277c8c098f23993d6a1eb17f14a0f11b54c4d42865a63d3cfe1914668eefb9a188e2de58f38b55a179a238fd1fef606893e194f
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10c0/ae00db89fe873064a093b8999fe4cc284b13ef2a178636211842cceb650b9c3e390d3339191acb145d81ed5379d2074840cf0c33a20bdbd6f32821f79eb4ad5d
  languageName: node
  linkType: hard

"image-size@npm:^1.0.2":
  version: 1.2.1
  resolution: "image-size@npm:1.2.1"
  dependencies:
    queue: "npm:6.0.2"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/f8b3c19d4476513f1d7e55c3e6db80997b315444743e2040d545cbcaee59be03d2eb40c46be949a8372697b7003fdb0c04925d704390a7f606bc8181e25c0ed4
  languageName: node
  linkType: hard

"immer@npm:^10.0.3":
  version: 10.1.1
  resolution: "immer@npm:10.1.1"
  checksum: 10c0/b749e10d137ccae91788f41bd57e9387f32ea6d6ea8fd7eb47b23fd7766681575efc7f86ceef7fe24c3bc9d61e38ff5d2f49c2663b2b0c056e280a4510923653
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: "npm:^2.0.0"
    resolve-from: "npm:^3.0.0"
  checksum: 10c0/116c55ee5215a7839062285b60df85dbedde084c02111dc58c1b9d03ff7876627059f4beb16cdc090a3db21fea9022003402aa782139dc8d6302589038030504
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-local@npm:^3.2.0":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: "npm:^4.2.0"
    resolve-cwd: "npm:^3.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10c0/94cd6367a672b7e0cb026970c85b76902d2710a64896fa6de93bd5c571dd03b228c5759308959de205083e3b1c61e799f019c9e36ee8e9c523b993e1057f0433
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^7.0.1":
  version: 7.0.1
  resolution: "inline-style-prefixer@npm:7.0.1"
  dependencies:
    css-in-js-utils: "npm:^3.1.0"
  checksum: 10c0/15da5a396b7f286b5b6742efe315218cd577bc96b43de08aeb76af7697d9f1ab3bfc66cf19fad2173957dd5d617a790240b9d51898bdcf4c2efb40d3f8bcb370
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"invariant@npm:2.2.4, invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-bun-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-bun-module@npm:2.0.0"
  dependencies:
    semver: "npm:^7.7.1"
  checksum: 10c0/7d27a0679cfa5be1f5052650391f9b11040cd70c48d45112e312c56bc6b6ca9c9aea70dcce6cc40b1e8947bfff8567a5c5715d3b066fb478522dab46ea379240
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: 10c0/1c39c7d1753b04e9483b89fb88908b8137ab4743b6f481947e97ccf93ecb384a814c8d3f0b95b082b149c5aa19c3e9e4464e2791d95174bce95998c26bb1974b
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 10c0/2957cab387997a466cd0bf5c1b6047bd21ecb32bdcfd8996b15747aa01002c1c88731802f1b3d34ac99f4f6874b626418bd118658cf39380fe5fff32a3af9c4d
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: 10c0/bcdcf6b8b9714063ffcfa9929c575ac69bfdabb8f4574ff557dfc086df2836cf07e3906f5bbc4f2a5c12f8f3ba56af640c843cdfc74da8caed86c7c7d66fd08e
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: 10c0/e5c9814cdaa627a9ad0a0964ded0e0491bfd9ace405c49a5d63c88b30a162f1512c069d5b80997893c4d0181eadc3fed02b4ab4b81059aba5620bfcdfdeb9c53
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10c0/8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0, istanbul-lib-instrument@npm:^6.0.2":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10c0/a1894e060dd2a3b9f046ffdc87b44c00a35516f5e6b7baf4910369acca79e506fc5323a816f811ae23d82334b38e3ddeb8b3b331bd2c860540793b59a8689128
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^5.0.0":
  version: 5.0.6
  resolution: "istanbul-lib-source-maps@npm:5.0.6"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.23"
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
  checksum: 10c0/ffe75d70b303a3621ee4671554f306e0831b16f39ab7f4ab52e54d356a5d33e534d97563e318f1333a6aae1d42f91ec49c76b6cd3f3fb378addcb5c81da0255f
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    get-proto: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/f7a262808e1b41049ab55f1e9c29af7ec1025a000d243b83edf34ce2416eedd56079b117fa59376bb4a724110690f13aa8427f2ee29a09eec63a7e72367626d0
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jest-changed-files@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-changed-files@npm:30.0.2"
  dependencies:
    execa: "npm:^5.1.1"
    jest-util: "npm:30.0.2"
    p-limit: "npm:^3.1.0"
  checksum: 10c0/794c9e47c460974f2303631d9ee44845d03f4ccd5240649a5f736aa94af78fa5931022324ab302c577dad6adb442ed17140dee9b9985bbfa0d43cad3048a7350
  languageName: node
  linkType: hard

"jest-circus@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-circus@npm:30.0.4"
  dependencies:
    "@jest/environment": "npm:30.0.4"
    "@jest/expect": "npm:30.0.4"
    "@jest/test-result": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    co: "npm:^4.6.0"
    dedent: "npm:^1.6.0"
    is-generator-fn: "npm:^2.1.0"
    jest-each: "npm:30.0.2"
    jest-matcher-utils: "npm:30.0.4"
    jest-message-util: "npm:30.0.2"
    jest-runtime: "npm:30.0.4"
    jest-snapshot: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    p-limit: "npm:^3.1.0"
    pretty-format: "npm:30.0.2"
    pure-rand: "npm:^7.0.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.6"
  checksum: 10c0/3953060de228baa7206b409eaa2fba04f1f7d7ace4e49da502ecb7fe3aca41c557c4f1279b4113934f0cae92b874ce5379e3bd719860e964701bd71aa70cfae6
  languageName: node
  linkType: hard

"jest-cli@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-cli@npm:30.0.4"
  dependencies:
    "@jest/core": "npm:30.0.4"
    "@jest/test-result": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    chalk: "npm:^4.1.2"
    exit-x: "npm:^0.2.2"
    import-local: "npm:^3.2.0"
    jest-config: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    jest-validate: "npm:30.0.2"
    yargs: "npm:^17.7.2"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: ./bin/jest.js
  checksum: 10c0/19ba715b6fe9575043c562e44aa2e4495e483ecaca39eadf1a0a37e54ed98897df63c99ea1ffa258346c1e4df4947109af1ee64e9d9f696016fc02f903c96077
  languageName: node
  linkType: hard

"jest-config@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-config@npm:30.0.4"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@jest/get-type": "npm:30.0.1"
    "@jest/pattern": "npm:30.0.1"
    "@jest/test-sequencer": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    babel-jest: "npm:30.0.4"
    chalk: "npm:^4.1.2"
    ci-info: "npm:^4.2.0"
    deepmerge: "npm:^4.3.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.11"
    jest-circus: "npm:30.0.4"
    jest-docblock: "npm:30.0.1"
    jest-environment-node: "npm:30.0.4"
    jest-regex-util: "npm:30.0.1"
    jest-resolve: "npm:30.0.2"
    jest-runner: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    jest-validate: "npm:30.0.2"
    micromatch: "npm:^4.0.8"
    parse-json: "npm:^5.2.0"
    pretty-format: "npm:30.0.2"
    slash: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  peerDependencies:
    "@types/node": "*"
    esbuild-register: ">=3.4.0"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    esbuild-register:
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/94e65ab2797a438a2fbf0354fbc7de562331d4b0d92a39f427bcfb03a6361db148a37008978794869b2095aa78d3227c4dbb565dc6295b6c00477ac028a1d102
  languageName: node
  linkType: hard

"jest-diff@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-diff@npm:30.0.4"
  dependencies:
    "@jest/diff-sequences": "npm:30.0.1"
    "@jest/get-type": "npm:30.0.1"
    chalk: "npm:^4.1.2"
    pretty-format: "npm:30.0.2"
  checksum: 10c0/aceae3a2e90ec232305ba43082e34ec5d24867459a6f52169e47edfd5f55457788ad534ff781d12e6606a70bc7ddc5090e45748732772679065dfd56f46f8ab1
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.6.3"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/89a4a7f182590f56f526443dde69acefb1f2f0c9e59253c61d319569856c4931eae66b8a3790c443f529267a0ddba5ba80431c585deed81827032b2b2a1fc999
  languageName: node
  linkType: hard

"jest-docblock@npm:30.0.1":
  version: 30.0.1
  resolution: "jest-docblock@npm:30.0.1"
  dependencies:
    detect-newline: "npm:^3.1.0"
  checksum: 10c0/f9bad2651db8afa029867ea7a40f422c9d73c67657360297371846a314a40c8786424be00483261df9137499f52c2af28cd458fbd15a7bf7fac8775b4bcd6ee1
  languageName: node
  linkType: hard

"jest-each@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-each@npm:30.0.2"
  dependencies:
    "@jest/get-type": "npm:30.0.1"
    "@jest/types": "npm:30.0.1"
    chalk: "npm:^4.1.2"
    jest-util: "npm:30.0.2"
    pretty-format: "npm:30.0.2"
  checksum: 10c0/6fff0a470d08ba3f0149c58266b7e938e3e183398f99065fe937290f1297ca254635f0f4bca6196514f756fac0a9759144b1c7f67bef97cc0b7fa0b96304df9e
  languageName: node
  linkType: hard

"jest-environment-node@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-environment-node@npm:30.0.4"
  dependencies:
    "@jest/environment": "npm:30.0.4"
    "@jest/fake-timers": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    jest-mock: "npm:30.0.2"
    jest-util: "npm:30.0.2"
    jest-validate: "npm:30.0.2"
  checksum: 10c0/3c2b5d30e459b3870a3fdd5aacf9f73b944b398cd07889bce850d45371357510bda9f45d7bdc39b71785351e16dcd47d836754a8a53f66b6454a43344e71bd22
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/61f04fec077f8b1b5c1a633e3612fc0c9aa79a0ab7b05600683428f1e01a4d35346c474bde6f439f9fcc1a4aa9a2861ff852d079a43ab64b02105d1004b2592b
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10c0/552e7a97a983d3c2d4e412a44eb7de0430ff773dd99f7500962c268d6dfbfa431d7d08f919c9d960530e5f7f78eb47f267ad9b318265e5092b3ff9ede0db7c2b
  languageName: node
  linkType: hard

"jest-haste-map@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-haste-map@npm:30.0.2"
  dependencies:
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    anymatch: "npm:^3.1.3"
    fb-watchman: "npm:^2.0.2"
    fsevents: "npm:^2.3.3"
    graceful-fs: "npm:^4.2.11"
    jest-regex-util: "npm:30.0.1"
    jest-util: "npm:30.0.2"
    jest-worker: "npm:30.0.2"
    micromatch: "npm:^4.0.8"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/6427b6976beb3fd33cae9a516e24f409d0cc0be2afa12a62e95671001a0d0d61662e8b2185027639b2036fe3e3b055e9d9b4dfd2063e787cf2a5d2140da0b80a
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/graceful-fs": "npm:^4.1.3"
    "@types/node": "npm:*"
    anymatch: "npm:^3.0.3"
    fb-watchman: "npm:^2.0.0"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.9"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/2683a8f29793c75a4728787662972fedd9267704c8f7ef9d84f2beed9a977f1cf5e998c07b6f36ba5603f53cb010c911fe8cd0ac9886e073fe28ca66beefd30c
  languageName: node
  linkType: hard

"jest-leak-detector@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-leak-detector@npm:30.0.2"
  dependencies:
    "@jest/get-type": "npm:30.0.1"
    pretty-format: "npm:30.0.2"
  checksum: 10c0/1df28475c40b41024adc6e18af0d3dc8d8d318fdbbf5c3560321fea0af2e0784c57f788b5b152efd83274ab6ea8dc3b36662060a83a2a555ffd8cdf7d628ee76
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-matcher-utils@npm:30.0.4"
  dependencies:
    "@jest/get-type": "npm:30.0.1"
    chalk: "npm:^4.1.2"
    jest-diff: "npm:30.0.4"
    pretty-format: "npm:30.0.2"
  checksum: 10c0/18f9f808e1de56a466d3a858acd5d253ea13e386619de05fe21b37316305b15feb078f12beae9228c878fc6b60b9bbbd1a6240f1878f80a222d241b38e54b53f
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/0d0e70b28fa5c7d4dce701dc1f46ae0922102aadc24ed45d594dd9b7ae0a8a6ef8b216718d1ab79e451291217e05d4d49a82666e1a3cc2b428b75cd9c933244e
  languageName: node
  linkType: hard

"jest-message-util@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-message-util@npm:30.0.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@jest/types": "npm:30.0.1"
    "@types/stack-utils": "npm:^2.0.3"
    chalk: "npm:^4.1.2"
    graceful-fs: "npm:^4.2.11"
    micromatch: "npm:^4.0.8"
    pretty-format: "npm:30.0.2"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.6"
  checksum: 10c0/c010d5b7d86e735e2fb4c4a220f57004349f488f5d4663240a7e9f2694d01b5228136540d55036777fde4227b5e0b56f08885b7f69395b295cab878357b1aeb1
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/850ae35477f59f3e6f27efac5215f706296e2104af39232bb14e5403e067992afb5c015e87a9243ec4d9df38525ef1ca663af9f2f4766aa116f127247008bd22
  languageName: node
  linkType: hard

"jest-mock@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-mock@npm:30.0.2"
  dependencies:
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    jest-util: "npm:30.0.2"
  checksum: 10c0/7728997c1d654475b88e18b7ba33a2a1b9f89ce33a9082bf2d14dcc3e831f372f80c762e481777886a3a04b4489ea5390ecdeb21c4def57fba5b2c77086a3959
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/7b9f8349ee87695a309fe15c46a74ab04c853369e5c40952d68061d9dc3159a0f0ed73e215f81b07ee97a9faaf10aebe5877a9d6255068a0977eae6a9ff1d5ac
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.3":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 10c0/86eec0c78449a2de733a6d3e316d49461af6a858070e113c97f75fb742a48c2396ea94150cbca44159ffd4a959f743a47a8b37a792ef6fdad2cf0a5cba973fac
  languageName: node
  linkType: hard

"jest-regex-util@npm:30.0.1":
  version: 30.0.1
  resolution: "jest-regex-util@npm:30.0.1"
  checksum: 10c0/f30c70524ebde2d1012afe5ffa5691d5d00f7d5ba9e43d588f6460ac6fe96f9e620f2f9b36a02d0d3e7e77bc8efb8b3450ae3b80ac53c8be5099e01bf54f6728
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 10c0/4e33fb16c4f42111159cafe26397118dcfc4cf08bc178a67149fb05f45546a91928b820894572679d62559839d0992e21080a1527faad65daaae8743a5705a3b
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-resolve-dependencies@npm:30.0.4"
  dependencies:
    jest-regex-util: "npm:30.0.1"
    jest-snapshot: "npm:30.0.4"
  checksum: 10c0/00675f375533a8d07606f91ce5bbb32269819df54b2f9d6dce28f26d9b014b383b69620806dba09953c9ed6bd0635799821fd5f14fb46d6aee4cfbcd27c41916
  languageName: node
  linkType: hard

"jest-resolve@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-resolve@npm:30.0.2"
  dependencies:
    chalk: "npm:^4.1.2"
    graceful-fs: "npm:^4.2.11"
    jest-haste-map: "npm:30.0.2"
    jest-pnp-resolver: "npm:^1.2.3"
    jest-util: "npm:30.0.2"
    jest-validate: "npm:30.0.2"
    slash: "npm:^3.0.0"
    unrs-resolver: "npm:^1.7.11"
  checksum: 10c0/33ae69455b1206a926bb6f7dd46cd4b6cbf5e095387078873a05dfb693bef419b93897e052ee68026b31b5e5f537fdcfce42f2d31af0ce7e64a8179ed7882b51
  languageName: node
  linkType: hard

"jest-runner@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-runner@npm:30.0.4"
  dependencies:
    "@jest/console": "npm:30.0.4"
    "@jest/environment": "npm:30.0.4"
    "@jest/test-result": "npm:30.0.4"
    "@jest/transform": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    emittery: "npm:^0.13.1"
    exit-x: "npm:^0.2.2"
    graceful-fs: "npm:^4.2.11"
    jest-docblock: "npm:30.0.1"
    jest-environment-node: "npm:30.0.4"
    jest-haste-map: "npm:30.0.2"
    jest-leak-detector: "npm:30.0.2"
    jest-message-util: "npm:30.0.2"
    jest-resolve: "npm:30.0.2"
    jest-runtime: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    jest-watcher: "npm:30.0.4"
    jest-worker: "npm:30.0.2"
    p-limit: "npm:^3.1.0"
    source-map-support: "npm:0.5.13"
  checksum: 10c0/22f33b5f2f9e54df7337ffd38e859fb7cfcb52dc858fc1b4ddc104f10d67e7ba882c3db937fe5d2988913957d6e23deb3b67d1c340c0344dbc3176c38ef2aa53
  languageName: node
  linkType: hard

"jest-runtime@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-runtime@npm:30.0.4"
  dependencies:
    "@jest/environment": "npm:30.0.4"
    "@jest/fake-timers": "npm:30.0.4"
    "@jest/globals": "npm:30.0.4"
    "@jest/source-map": "npm:30.0.1"
    "@jest/test-result": "npm:30.0.4"
    "@jest/transform": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    cjs-module-lexer: "npm:^2.1.0"
    collect-v8-coverage: "npm:^1.0.2"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.11"
    jest-haste-map: "npm:30.0.2"
    jest-message-util: "npm:30.0.2"
    jest-mock: "npm:30.0.2"
    jest-regex-util: "npm:30.0.1"
    jest-resolve: "npm:30.0.2"
    jest-snapshot: "npm:30.0.4"
    jest-util: "npm:30.0.2"
    slash: "npm:^3.0.0"
    strip-bom: "npm:^4.0.0"
  checksum: 10c0/75147405c6896ff717d4c64f9c628c860b18fa6f8c959ffe3c0757d0470b4cead728389b198142119e00fb1a4ba2dd938021a67a07f3679b1d7930ba3f9a2523
  languageName: node
  linkType: hard

"jest-snapshot@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-snapshot@npm:30.0.4"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@babel/generator": "npm:^7.27.5"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.3"
    "@jest/expect-utils": "npm:30.0.4"
    "@jest/get-type": "npm:30.0.1"
    "@jest/snapshot-utils": "npm:30.0.4"
    "@jest/transform": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    babel-preset-current-node-syntax: "npm:^1.1.0"
    chalk: "npm:^4.1.2"
    expect: "npm:30.0.4"
    graceful-fs: "npm:^4.2.11"
    jest-diff: "npm:30.0.4"
    jest-matcher-utils: "npm:30.0.4"
    jest-message-util: "npm:30.0.2"
    jest-util: "npm:30.0.2"
    pretty-format: "npm:30.0.2"
    semver: "npm:^7.7.2"
    synckit: "npm:^0.11.8"
  checksum: 10c0/d33cf08de392883797f78e3815035b90a63b75ce3bd85d3a5726622310c830095e25c5579be961fd06faf0a8381d671407a92a7b2e97edc0f37a37d9047760d1
  languageName: node
  linkType: hard

"jest-util@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-util@npm:30.0.2"
  dependencies:
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    chalk: "npm:^4.1.2"
    ci-info: "npm:^4.2.0"
    graceful-fs: "npm:^4.2.11"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/07de384790b8e5a5925fba5448fa1475790a5b52271fbf99958c18e468da1af940f8b45e330d87766576cf6c5d1f4f41ce51c976483a5079653d9fcdba8aac8e
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10c0/bc55a8f49fdbb8f51baf31d2a4f312fb66c9db1483b82f602c9c990e659cdd7ec529c8e916d5a89452ecbcfae4949b21b40a7a59d4ffc0cd813a973ab08c8150
  languageName: node
  linkType: hard

"jest-validate@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-validate@npm:30.0.2"
  dependencies:
    "@jest/get-type": "npm:30.0.1"
    "@jest/types": "npm:30.0.1"
    camelcase: "npm:^6.3.0"
    chalk: "npm:^4.1.2"
    leven: "npm:^3.1.0"
    pretty-format: "npm:30.0.2"
  checksum: 10c0/9fd1b4f604851187655353eefe8db25db9638dd312d2e29d58868e626d78925edefe94fe2c8eb63305eefd41e5fe7f8aff334e2db9db5aaddeec866f9f6561d8
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/a20b930480c1ed68778c739f4739dce39423131bc070cd2505ddede762a5570a256212e9c2401b7ae9ba4d7b7c0803f03c5b8f1561c62348213aba18d9dbece2
  languageName: node
  linkType: hard

"jest-watcher@npm:30.0.4":
  version: 30.0.4
  resolution: "jest-watcher@npm:30.0.4"
  dependencies:
    "@jest/test-result": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.3.2"
    chalk: "npm:^4.1.2"
    emittery: "npm:^0.13.1"
    jest-util: "npm:30.0.2"
    string-length: "npm:^4.0.2"
  checksum: 10c0/19649fb4998f05a9d44bf37456e42c85b43bb4cdd8b12e23be992f627cbcc912c86001b69e0bcb5fd5ba0eded6d9c600cd855beb38621edfcacf5e2f29f2d2a7
  languageName: node
  linkType: hard

"jest-worker@npm:30.0.2":
  version: 30.0.2
  resolution: "jest-worker@npm:30.0.2"
  dependencies:
    "@types/node": "npm:*"
    "@ungap/structured-clone": "npm:^1.3.0"
    jest-util: "npm:30.0.2"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.1.1"
  checksum: 10c0/d7d237e763a2f1aed4eba07f977490442a7bb085f7ab63163afa88776804c2644cc05a1e32da9d05a4b895ad22b2e939ef01a90ffb3024b53fc8c73b8ad1d3f1
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/5570a3a005b16f46c131968b8a5b56d291f9bbb85ff4217e31c80bd8a02e7de799e59a54b95ca28d5c302f248b54cbffde2d177c2f0f52ffcee7504c6eabf660
  languageName: node
  linkType: hard

"jest@npm:^30.0.4":
  version: 30.0.4
  resolution: "jest@npm:30.0.4"
  dependencies:
    "@jest/core": "npm:30.0.4"
    "@jest/types": "npm:30.0.1"
    import-local: "npm:^3.2.0"
    jest-cli: "npm:30.0.4"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: ./bin/jest.js
  checksum: 10c0/57ef5001f85a502a503440636ecd183cbf9a7b68ffadf0f28adb7d436f2fc07e738ef4f45e9f7c691ad051bb9a6a6520301287628678cc67c42433a022edfaa3
  languageName: node
  linkType: hard

"jimp-compact@npm:0.16.1":
  version: 0.16.1
  resolution: "jimp-compact@npm:0.16.1"
  checksum: 10c0/2d73bb927d840ce6dc093d089d770eddbb81472635ced7cad1d7c4545d8734aecf5bd3dedf7178a6cfab4d06c9d6cbbf59e5cb274ed99ca11cd4835a6374f16c
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 10c0/77b61989c758ff32407cdae8ddc77f85e18e1a13fc4977110dbd2e05fc761842f5f71bce684d9a01316e1c4263971315a111385759951080bbfe17cbb5de8f7a
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsc-safe-url@npm:^0.2.2, jsc-safe-url@npm:^0.2.4":
  version: 0.2.4
  resolution: "jsc-safe-url@npm:0.2.4"
  checksum: 10c0/429bd645f8a35938f08f5b01c282e5ef55ed8be30a9ca23517b7ca01dcbf84b4b0632042caceab50f8f5c0c1e76816fe3c74de3e59be84da7f89ae1503bd3c68
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: 10c0/2f1287a7c833e397c9ddd361a78638e828fc523038bb3441fd4fc144cfd2c6cd4963ffb9e207e648cf7b692600f1e1e524e965c32df5152120910e4903a47dcb
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10c0/9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: 10c0/a32679e9cb55469cb6d8bbc863f7d631b2c98b7fc7bf172629261751a6e7bc8da6ae374ddb74d5fbd8b06cf0eb4572287b259813d92b36e384024ed35e4c13e1
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"lan-network@npm:^0.1.6":
  version: 0.1.7
  resolution: "lan-network@npm:0.1.7"
  bin:
    lan-network: dist/lan-network-cli.js
  checksum: 10c0/7afd3a7159bb65ff40bded481e4d522b1faa6b65e8b69d6404651d87fe800a35510aff9b913bb90def4f66ca886e28907492b8323f8c568830b42d28f521fb18
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lighthouse-logger@npm:^1.0.0":
  version: 1.4.2
  resolution: "lighthouse-logger@npm:1.4.2"
  dependencies:
    debug: "npm:^2.6.9"
    marky: "npm:^1.2.2"
  checksum: 10c0/090431db34e9ce01b03b2a03b39e998807a7a86214f2e8da2ba9588c36841caf4474f96ef1b2deaf9fe58f2e00f9f51618e0b98edecc2d8c9dfc13185bf0adc8
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-arm64@npm:1.27.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-arm64@npm:1.30.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-x64@npm:1.27.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-x64@npm:1.30.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-freebsd-x64@npm:1.27.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-freebsd-x64@npm:1.30.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.27.0"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.30.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-musl@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-musl@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-musl@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-musl@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-arm64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-arm64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-x64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-x64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:^1.27.0":
  version: 1.30.1
  resolution: "lightningcss@npm:1.30.1"
  dependencies:
    detect-libc: "npm:^2.0.3"
    lightningcss-darwin-arm64: "npm:1.30.1"
    lightningcss-darwin-x64: "npm:1.30.1"
    lightningcss-freebsd-x64: "npm:1.30.1"
    lightningcss-linux-arm-gnueabihf: "npm:1.30.1"
    lightningcss-linux-arm64-gnu: "npm:1.30.1"
    lightningcss-linux-arm64-musl: "npm:1.30.1"
    lightningcss-linux-x64-gnu: "npm:1.30.1"
    lightningcss-linux-x64-musl: "npm:1.30.1"
    lightningcss-win32-arm64-msvc: "npm:1.30.1"
    lightningcss-win32-x64-msvc: "npm:1.30.1"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/1e1ad908f3c68bf39d964a6735435a8dd5474fb2765076732d64a7b6aa2af1f084da65a9462443a9adfebf7dcfb02fb532fce1d78697f2a9de29c8f40f09aee3
  languageName: node
  linkType: hard

"lightningcss@npm:~1.27.0":
  version: 1.27.0
  resolution: "lightningcss@npm:1.27.0"
  dependencies:
    detect-libc: "npm:^1.0.3"
    lightningcss-darwin-arm64: "npm:1.27.0"
    lightningcss-darwin-x64: "npm:1.27.0"
    lightningcss-freebsd-x64: "npm:1.27.0"
    lightningcss-linux-arm-gnueabihf: "npm:1.27.0"
    lightningcss-linux-arm64-gnu: "npm:1.27.0"
    lightningcss-linux-arm64-musl: "npm:1.27.0"
    lightningcss-linux-x64-gnu: "npm:1.27.0"
    lightningcss-linux-x64-musl: "npm:1.27.0"
    lightningcss-win32-arm64-msvc: "npm:1.27.0"
    lightningcss-win32-x64-msvc: "npm:1.27.0"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/5292b277ebbefdd952cb7b9ccd20dd2c185a7eae9b4393960386b7b8c4d644492a413a91d05ca9dcb72c775bbb8d79b235a3415d66410c47464039394d022109
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 10c0/f5604e7240c5c275743561442fbc5abf2a84ad94da0f5adc71d25e31fa8483048de3dcedcb7a44112a942fed305fd75841cdf6c9681c7f640c63f1049e9a5dcc
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.debounce@npm:4.0.8, lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 10c0/14628013e9e7f65ac904fc82fd8ecb0e55a9c4c2416434b1dd9cf64ae70a8937f0b15376a39a68248530adc64887ed0fe2b75204b2c9ec3eea1cb2d66ddd125d
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: "npm:^2.0.1"
  checksum: 10c0/574eb4205f54f0605021aa67ebb372c30ca64e8ddd439efeb8507af83c776dce789e83614e80059014d9e48dcc94c4b60cef2e85f0dc944eea27c799cec62353
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lottie-react-native@npm:7.2.2":
  version: 7.2.2
  resolution: "lottie-react-native@npm:7.2.2"
  peerDependencies:
    "@lottiefiles/dotlottie-react": ^0.6.5
    react: "*"
    react-native: ">=0.46"
    react-native-windows: ">=0.63.x"
  peerDependenciesMeta:
    "@lottiefiles/dotlottie-react":
      optional: true
    react-native-windows:
      optional: true
  checksum: 10c0/d0ee7c6ed639ad1b5cfe04dd3b704f3f968806c59a6361986fbb22ebd1b332d9cb13ee7d78ed49848f8d416d468ae2fdc9077815280023dac0b66e841b5ce840
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lucide-react-native@npm:^0.514.0":
  version: 0.514.0
  resolution: "lucide-react-native@npm:0.514.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-native: "*"
    react-native-svg: ^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0
  checksum: 10c0/2ee824933e047bf32c34d883b622843c973782d57a3d0f8913cb0e6b746999e60912ddc756996a3f0dd4872a130a0be6d88f31e45c7aa27f525e7fe84da82364
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10c0/b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"marky@npm:^1.2.2":
  version: 1.3.0
  resolution: "marky@npm:1.3.0"
  checksum: 10c0/6619cdb132fdc4f7cd3e2bed6eebf81a38e50ff4b426bbfb354db68731e4adfebf35ebfd7c8e5a6e846cbf9b872588c4f76db25782caee8c1529ec9d483bf98b
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 10c0/67241f8708c1e665a061d2b042d2d243366e93e5bf1f917693007f6d55111588b952dcbfd3ea9c2d0969fb754aad81b30fdcfdcc24546495fc3b24336b28d4bd
  languageName: node
  linkType: hard

"memoize-one@npm:^5.0.0, memoize-one@npm:^5.2.1":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: 10c0/fd22dbe9a978a2b4f30d6a491fc02fb90792432ad0dab840dc96c1734d2bd7c9cdeb6a26130ec60507eb43230559523615873168bcbe8fafab221c30b11d54c1
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: 10c0/45c88e064fd715166619af72e8cf8a7a17224d6edf61f7a8633d740ed8c8c0558a4373876c9b8ffc5518c2b65a960266adf403cc215cb1e90f7e262b58991f54
  languageName: node
  linkType: hard

"merge-options@npm:^3.0.4":
  version: 3.0.4
  resolution: "merge-options@npm:3.0.4"
  dependencies:
    is-plain-obj: "npm:^2.1.0"
  checksum: 10c0/02b5891ceef09b0b497b5a0154c37a71784e68ed71b14748f6fd4258ffd3fe4ecd5cb0fd6f7cae3954fd11e7686c5cb64486daffa63c2793bbe8b614b61c7055
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"metro-babel-transformer@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-babel-transformer@npm:0.82.4"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    hermes-parser: "npm:0.28.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/39e1d9d49395fc4d1877b3202e1353dd7a5639911d52a164b4920d6ac36a001a6165e15d38c7142ed3989369ae68611aadd37ab4ce473c4826045eac36b16998
  languageName: node
  linkType: hard

"metro-cache-key@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-cache-key@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/0266ad4439caa05dac334f2acfd06da5c74465ec11aebc59ec802051b46338a553915cc00ec6040afbb2b8c7e23c7fc383d000316a1c5c2d7592686c94486ff8
  languageName: node
  linkType: hard

"metro-cache@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-cache@npm:0.82.4"
  dependencies:
    exponential-backoff: "npm:^3.1.1"
    flow-enums-runtime: "npm:^0.0.6"
    https-proxy-agent: "npm:^7.0.5"
    metro-core: "npm:0.82.4"
  checksum: 10c0/41213b29600c17e27d2d6eb9f72358da734a683e3f6917703c6c1e57f35a64e036ac3adcd54df4608d049d19dc59ee5193fa65a9f0f0c72d4fe25a0eb1d5a89a
  languageName: node
  linkType: hard

"metro-config@npm:0.82.4, metro-config@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-config@npm:0.82.4"
  dependencies:
    connect: "npm:^3.6.5"
    cosmiconfig: "npm:^5.0.5"
    flow-enums-runtime: "npm:^0.0.6"
    jest-validate: "npm:^29.7.0"
    metro: "npm:0.82.4"
    metro-cache: "npm:0.82.4"
    metro-core: "npm:0.82.4"
    metro-runtime: "npm:0.82.4"
  checksum: 10c0/b056d859e208e832c4a8dbc88ce2678bc89f1b06043493c45c7ce7eb883f2aeee80144aa03c9c2758f47babbba18ea420975461d31b89435b1405e2ab05428e3
  languageName: node
  linkType: hard

"metro-core@npm:0.82.4, metro-core@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-core@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    lodash.throttle: "npm:^4.1.1"
    metro-resolver: "npm:0.82.4"
  checksum: 10c0/faa73a49aebc430edfd221135288bbad5e93695c3fed70cab6976a34b3c48fb74e4c20037237afe53d674e00563cb0df7cf074202deaa182f965ead71de12508
  languageName: node
  linkType: hard

"metro-file-map@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-file-map@npm:0.82.4"
  dependencies:
    debug: "npm:^4.4.0"
    fb-watchman: "npm:^2.0.0"
    flow-enums-runtime: "npm:^0.0.6"
    graceful-fs: "npm:^4.2.4"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    nullthrows: "npm:^1.1.1"
    walker: "npm:^1.0.7"
  checksum: 10c0/a3792278e948cb9cbe5da8aeba6e3ba1f43b86f3e0f53768c2222ba457fc869ec3b91de95d6eb29551c079e2497de282e2c984fecd5f7d0c1af0345c35abddc1
  languageName: node
  linkType: hard

"metro-minify-terser@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-minify-terser@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    terser: "npm:^5.15.0"
  checksum: 10c0/aa99ca504215106edc0b4ddc48de34ac3b9aa134c4e827b4a85670b31b0006742a8ce6ec1472afbb05ca6c610476c8edae78fe43e1208e6988f1f2e9297b7160
  languageName: node
  linkType: hard

"metro-resolver@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-resolver@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/934fdb3345c0ed827afb4bde91ea951b6087665f7011bac58a7824f1f23f6b1b4daa7526b16ecba02eb9450e7ff06bb28c391731ae5ab73d27603ebc2b088280
  languageName: node
  linkType: hard

"metro-runtime@npm:0.82.4, metro-runtime@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-runtime@npm:0.82.4"
  dependencies:
    "@babel/runtime": "npm:^7.25.0"
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/dfb864511858503b68d1a21ca19b03fba7a4fc29693e3fb1b8f0e0175928d63f7ac60ec6722805267c2f091f95c60cf744cb0e59cd221023d680a2a7336cb92e
  languageName: node
  linkType: hard

"metro-source-map@npm:0.82.4, metro-source-map@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro-source-map@npm:0.82.4"
  dependencies:
    "@babel/traverse": "npm:^7.25.3"
    "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3"
    "@babel/types": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-symbolicate: "npm:0.82.4"
    nullthrows: "npm:^1.1.1"
    ob1: "npm:0.82.4"
    source-map: "npm:^0.5.6"
    vlq: "npm:^1.0.0"
  checksum: 10c0/049000fe3aefab89744d22b638a635de855bccc3ae343ef190a3f646b4676b01686a5b101c59a3bc336fa84a5b4cfeaca158563edd121786069f5a9343640f17
  languageName: node
  linkType: hard

"metro-symbolicate@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-symbolicate@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-source-map: "npm:0.82.4"
    nullthrows: "npm:^1.1.1"
    source-map: "npm:^0.5.6"
    vlq: "npm:^1.0.0"
  bin:
    metro-symbolicate: src/index.js
  checksum: 10c0/88f6bb6dd1235567e582a47d1abdc4605ba5ea841cb74bb88a2c35dde723e5ef7ec786665b6db9dbd5a8ad5a1bca2194fe7d181d01e3d39fbe6734d0c23d889e
  languageName: node
  linkType: hard

"metro-transform-plugins@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-transform-plugins@npm:0.82.4"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/template": "npm:^7.25.0"
    "@babel/traverse": "npm:^7.25.3"
    flow-enums-runtime: "npm:^0.0.6"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/d11d10194aca1202ed7b08aad82f6da3fad2fd4e57b66eea11f16100684cc0b4619e08a9654ae486483031ec39211fb786b67dc2bc56ab88c462755e6e988fe4
  languageName: node
  linkType: hard

"metro-transform-worker@npm:0.82.4":
  version: 0.82.4
  resolution: "metro-transform-worker@npm:0.82.4"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/parser": "npm:^7.25.3"
    "@babel/types": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    metro: "npm:0.82.4"
    metro-babel-transformer: "npm:0.82.4"
    metro-cache: "npm:0.82.4"
    metro-cache-key: "npm:0.82.4"
    metro-minify-terser: "npm:0.82.4"
    metro-source-map: "npm:0.82.4"
    metro-transform-plugins: "npm:0.82.4"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/30433b857fb3719ddc67e8cfb50589f67749e1b9c6ac77b1e1d6b8be91be6631998f0ecda0cf4decb4e4dfd1f65cc0fc92a9ead7468e0f58d80b89e78ffdb8e1
  languageName: node
  linkType: hard

"metro@npm:0.82.4, metro@npm:^0.82.0":
  version: 0.82.4
  resolution: "metro@npm:0.82.4"
  dependencies:
    "@babel/code-frame": "npm:^7.24.7"
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/parser": "npm:^7.25.3"
    "@babel/template": "npm:^7.25.0"
    "@babel/traverse": "npm:^7.25.3"
    "@babel/types": "npm:^7.25.2"
    accepts: "npm:^1.3.7"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^2.0.0"
    connect: "npm:^3.6.5"
    debug: "npm:^4.4.0"
    error-stack-parser: "npm:^2.0.6"
    flow-enums-runtime: "npm:^0.0.6"
    graceful-fs: "npm:^4.2.4"
    hermes-parser: "npm:0.28.1"
    image-size: "npm:^1.0.2"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.7.0"
    jsc-safe-url: "npm:^0.2.2"
    lodash.throttle: "npm:^4.1.1"
    metro-babel-transformer: "npm:0.82.4"
    metro-cache: "npm:0.82.4"
    metro-cache-key: "npm:0.82.4"
    metro-config: "npm:0.82.4"
    metro-core: "npm:0.82.4"
    metro-file-map: "npm:0.82.4"
    metro-resolver: "npm:0.82.4"
    metro-runtime: "npm:0.82.4"
    metro-source-map: "npm:0.82.4"
    metro-symbolicate: "npm:0.82.4"
    metro-transform-plugins: "npm:0.82.4"
    metro-transform-worker: "npm:0.82.4"
    mime-types: "npm:^2.1.27"
    nullthrows: "npm:^1.1.1"
    serialize-error: "npm:^2.1.0"
    source-map: "npm:^0.5.6"
    throat: "npm:^5.0.0"
    ws: "npm:^7.5.10"
    yargs: "npm:^17.6.2"
  bin:
    metro: src/cli.js
  checksum: 10c0/8bcdad2a7ff3fedb31f07732aac4b95852e420f114d03b76be6b0ffb3d76eff42e0cbc7d060b6431e8962a76597ae15c8f4f8e8123881bcbc5a83f0bdb9055bd
  languageName: node
  linkType: hard

"microdiff@npm:^1.5.0":
  version: 1.5.0
  resolution: "microdiff@npm:1.5.0"
  checksum: 10c0/5f55ca049451d63abf79fd1692808e1052180fbedfc91d9fcab9f02df0dd902aa6a4e30199fd345d1f91982842b12d85ffa86785e38d7dbfb6d59280074f9129
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10c0/8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 10c0/ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 10c0/7e207bd5c20401b292de291f02913230cb1163abca162044f7db1d951fa245b174dc00869d40dd9a9f32a885ad6a5f3e767ee104cf278f399cb4e92d3f582d5c
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mitt@npm:^3.0.1":
  version: 3.0.1
  resolution: "mitt@npm:3.0.1"
  checksum: 10c0/3ab4fdecf3be8c5255536faa07064d05caa3dd332bd318ff02e04621f7b3069ca1de9106cfe8e7ced675abfc2bec2ce4c4ef321c4a1bb1fb29df8ae090741913
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"moment@npm:^2.29.4":
  version: 2.30.1
  resolution: "moment@npm:2.30.1"
  checksum: 10c0/865e4279418c6de666fca7786607705fd0189d8a7b7624e2e56be99290ac846f90878a6f602e34b4e0455c549b85385b1baf9966845962b313699e7cb847543a
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 10c0/103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.1, nanoid@npm:^3.3.11, nanoid@npm:^3.3.7":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.2.2":
  version: 0.2.4
  resolution: "napi-postinstall@npm:0.2.4"
  bin:
    napi-postinstall: lib/cli.js
  checksum: 10c0/e8c357d7e27848c4af7becf2796afff245a2fc8ba176e1b133410bb1c9934a66d4bc542d0c9f04c73b0ba34ee0486b30b6cd1c62ed3aa36797d394200c9a2a8b
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.3.0":
  version: 0.3.0
  resolution: "napi-postinstall@npm:0.3.0"
  bin:
    napi-postinstall: lib/cli.js
  checksum: 10c0/dd5b295a0c7e669dda81a553b5defcdbe56805beb4279cd0df973454f072c083f574d399c4c825eece128113159658b031b4ac4b9dcb5735c5e34ddaefd3a3ca
  languageName: node
  linkType: hard

"nativewind@npm:^4.1.23":
  version: 4.1.23
  resolution: "nativewind@npm:4.1.23"
  dependencies:
    comment-json: "npm:^4.2.5"
    debug: "npm:^4.3.7"
    react-native-css-interop: "npm:0.1.22"
  peerDependencies:
    tailwindcss: ">3.3.0"
  checksum: 10c0/135c9a07afdc59d9f9f5a9d0d80c97766694c39243dd0eb21b01001814447792d7eca358c276b89f62d4d060c30a1de9732a0bb676590ac3a194da15ef3dea5f
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"nested-error-stacks@npm:~2.0.1":
  version: 2.0.1
  resolution: "nested-error-stacks@npm:2.0.1"
  checksum: 10c0/125049632bc3ca2252e994ca07f27d795c0e6decc4077f0f4163348d30d7cb95409ceff6184284c95396aa5ea8ff5010673063db7674058b966b4f0228d4981c
  languageName: node
  linkType: hard

"node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-forge@npm:^1.2.1, node-forge@npm:^1.3.1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 10c0/e882819b251a4321f9fc1d67c85d1501d3004b4ee889af822fd07f64de3d1a8e272ff00b689570af0465d65d6bf5074df9c76e900e0aff23e60b847f2a46fbe8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10c0/a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-package-arg@npm:^11.0.0":
  version: 11.0.3
  resolution: "npm-package-arg@npm:11.0.3"
  dependencies:
    hosted-git-info: "npm:^7.0.0"
    proc-log: "npm:^4.0.0"
    semver: "npm:^7.3.5"
    validate-npm-package-name: "npm:^5.0.0"
  checksum: 10c0/e18333485e05c3a8774f4b5701ef74f4799533e650b70a68ca8dd697666c9a8d46932cb765fc593edce299521033bd4025a40323d5240cea8a393c784c0c285a
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10c0/56f34bd7c3dcb3bd23481a277fa22918120459d3e9d95ca72976c72e9cac33a97483f0b95fc420e2eb546b9fe6db398273aba9a938650cdb8c98ee8f159dcb30
  languageName: node
  linkType: hard

"ob1@npm:0.82.4":
  version: 0.82.4
  resolution: "ob1@npm:0.82.4"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/c1958a96531a9b381c0a0843d7ff8a6937adbc6fab266548a61e02f0d7b882e1fb49c94350a38693546930b5be99bdab2757ffabed5271238887f62522afe1cb
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 10c0/a06844537107b960c1c8b96cd2ac8592a265186bfa0f6ccafe0d34eabdb526f6fa81da1f37c43df7ed13b12a4ae3457a16071603bcd39d8beddb5f08c37b0f47
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.1.1"
  checksum: 10c0/d4b8c1e586650407da03370845f029aa14076caca4e4d4afadbc69cfb5b78035fd3ee7be417141abdb0258fa142e59b11923b4c44d8b1255b28f5ffcc50da7db
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/cd4327e6c3369cfa805deb4cbbe919bfb7d3aeebf0bcaba291bb568ea7169f8f8cdbcabe2f00b40db0c20cd20f08e11b5f3a5a36fb7dd3fe04850c50db3bf83b
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
  checksum: 10c0/60d0455c85c736fbfeda0217d1a77525956f76f7b2495edeca9e9bbf8168a45783199e77b894d30638837c654d0cc410e0e02cbfcf445bc8de71c3da1ede6a9c
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.0, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/c904f9e518b11941eb60279a3cbfaf1289bd0001f600a950255b1dede9fe3df8cd74f38483550b3bb9485165166acb5db500c3b4c4337aec2815c88c96fcc2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10c0/f649e65c197bf31505a4c0444875db0258e198292f34b884d73c2f751e91792ef96bb5cf89aa0f4fecc2e4dc662461dda606b1274b0e564f539cae5d2f5fc32f
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: "npm:^1.0.0"
  checksum: 10c0/b4e44a8c34e70e02251bfb578a6e26d6de6eedbed106cd78211d2fd64d28b6281d54924696554e4e966559644243753ac5df73c87f283b0927533d3315696215
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:^7.0.3":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: "npm:^2.0.0"
    is-wsl: "npm:^2.1.1"
  checksum: 10c0/77573a6a68f7364f3a19a4c80492712720746b63680ee304555112605ead196afe91052bd3c3d165efdf4e9d04d255e87de0d0a77acec11ef47fd5261251813f
  languageName: node
  linkType: hard

"open@npm:^8.0.4":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: "npm:^2.4.2"
    cli-cursor: "npm:^2.1.0"
    cli-spinners: "npm:^2.0.0"
    log-symbols: "npm:^2.2.0"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/04cb375f222c36a16a95e6c39c473644a99a42fc34d35c37507cb836ea0a71f4d831fcd53198a460869114b2730891d63cc1047304afe5ddb078974d468edfb1
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: "npm:^1.3.1"
    json-parse-better-errors: "npm:^1.0.1"
  checksum: 10c0/8d80790b772ccb1bcea4e09e2697555e519d83d04a77c2b4237389b813f82898943a93ffff7d0d2406203bdd0c30dcf95b1661e3a53f83d0e417f053957bef32
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-png@npm:^2.1.0":
  version: 2.1.0
  resolution: "parse-png@npm:2.1.0"
  dependencies:
    pngjs: "npm:^3.3.0"
  checksum: 10c0/5157a8bbb976ae1ca990fc53c7014d42aac0967cb30e2daf36c3fef1876c8db0d551e695400c904f33c5c5add76a572c65b5044721d62417d8cc7abe4c4ffa41
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.5, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^3.0.1":
  version: 3.0.1
  resolution: "picomatch@npm:3.0.1"
  checksum: 10c0/70ec738569f1864658378b7abdab8939d15dae0718c1df994eae3346fd33daf6a3c1ff4e0c1a0cd1e2c0319130985b63a2cff34d192f2f2acbb78aca76111736
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10c0/551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1, pirates@npm:^4.0.4, pirates@npm:^4.0.7":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 10c0/a51f108dd811beb779d58a76864bbd49e239fa40c7984cd11596c75a121a8cc789f1c8971d8bb15f0dbf9d48b76c05bb62fcbce840f89b688c0fa64b37e8478a
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10c0/c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.5.1"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/db19ba50faafc4103df8e79bcd6b08004a56db2a9dd30b3e5c8b0ef30398ef44344a674e594d012c8fc39e539a2b72cb58c60a76b4b4401cbbc7c8f6b028d93d
  languageName: node
  linkType: hard

"pngjs@npm:^3.3.0":
  version: 3.4.0
  resolution: "pngjs@npm:3.4.0"
  checksum: 10c0/88ee73e2ad3f736e0b2573722309eb80bd2aa28916f0862379b4fd0f904751b4f61bb6bd1ecd7d4242d331f2b5c28c13309dd4b7d89a9b78306e35122fdc5011
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: "npm:^4.0.0"
    read-cache: "npm:^1.0.0"
    resolve: "npm:^1.1.7"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10c0/518aee5c83ea6940e890b0be675a2588db68b2582319f48c3b4e06535a50ea6ee45f7e63e4309f8754473245c47a0372632378d1d73d901310f295a92f26f17b
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: "npm:^2.0.1"
  peerDependencies:
    postcss: ^8.4.21
  checksum: 10c0/af35d55cb873b0797d3b42529514f5318f447b134541844285c9ac31a17497297eb72296902967911bb737a75163441695737300ce2794e3bd8c70c13a3b106e
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: "npm:^3.0.0"
    yaml: "npm:^2.3.4"
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/3d7939acb3570b0e4b4740e483d6e555a3e2de815219cb8a3c8fc03f575a6bde667443aa93369c0be390af845cb84471bf623e24af833260de3a105b78d42519
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: "npm:^6.1.1"
  peerDependencies:
    postcss: ^8.2.14
  checksum: 10c0/7f9c3f2d764191a39364cbdcec350f26a312431a569c9ef17408021424726b0d67995ff5288405e3724bb7152a4c92f73c027e580ec91e798800ed3c52e2bc6e
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/523196a6bd8cf660bdf537ad95abd79e546d54180f9afb165a4ab3e651ac705d0f8b8ce6b3164fb9e3279ce482c5f751a69eb2d3a1e8eb0fd5e82294fb3ef13e
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:^8.4.47":
  version: 8.5.4
  resolution: "postcss@npm:8.5.4"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/0feff648614a834f7cd5396ea6b05b658ca0507e10a4eaad03b56c348f6aec93f42a885fc1b30522630c6a7e49ae53b38a061e3cba526f2d9857afbe095a22bb
  languageName: node
  linkType: hard

"postcss@npm:~8.4.32":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/f1b3f17aaf36d136f59ec373459f18129908235e65dbdc3aee5eef8eba0756106f52de5ec4682e29a2eab53eb25170e7e871b3e4b52a8f1de3d344a514306be3
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10c0/81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:^3.5.3":
  version: 3.5.3
  resolution: "prettier@npm:3.5.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/3880cb90b9dc0635819ab52ff571518c35bd7f15a6e80a2054c05dbc8a3aa6e74f135519e91197de63705bcb38388ded7e7230e2178432a1468005406238b877
  languageName: node
  linkType: hard

"pretty-bytes@npm:^5.6.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 10c0/f69f494dcc1adda98dbe0e4a36d301e8be8ff99bfde7a637b2ee2820e7cb583b0fc0f3a63b0e3752c01501185a5cf38602c7be60da41bdf84ef5b70e89c370f3
  languageName: node
  linkType: hard

"pretty-format@npm:30.0.2, pretty-format@npm:^30.0.0":
  version: 30.0.2
  resolution: "pretty-format@npm:30.0.2"
  dependencies:
    "@jest/schemas": "npm:30.0.1"
    ansi-styles: "npm:^5.2.0"
    react-is: "npm:^18.3.1"
  checksum: 10c0/cf542dc2d0be95e2b1c6e3a397a4fc13fce1c9f8feed6b56165c0d23c7a83423abb6b032ed8e3e1b7c1c0709f9b117dd30b5185f107e58f8766616be6de84850
  languageName: node
  linkType: hard

"pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/edc5ff89f51916f036c62ed433506b55446ff739358de77207e63e88a28ca2894caac6e73dcb68166a606e51c8087d32d400473e6a9fdd2dbe743f46c9c0276f
  languageName: node
  linkType: hard

"proc-log@npm:^4.0.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 10c0/17db4757c2a5c44c1e545170e6c70a26f7de58feb985091fb1763f5081cab3d01b181fb2dd240c9f4a4255a1d9227d163d5771b7e69c9e49a561692db865efb9
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: 10c0/1697e07cb1068055dbe9fe858d242368ff5d2073639e652b75a7eb1f2a1a8d4afd404d719de23c7b48481a6aa0040686310e2dac2f53d776daa2176d3f96369c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: "npm:~2.0.3"
  checksum: 10c0/742e5c0cc646af1f0746963b8776299701ad561ce2c70b49365d62c8db8ea3681b0a1bf0d4e2fe07910bf72f02d39e51e8e73dc8d7503c3501206ac908be107f
  languageName: node
  linkType: hard

"promise@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: "npm:~2.0.6"
  checksum: 10c0/6fccae27a10bcce7442daf090279968086edd2e3f6cebe054b71816403e2526553edf510d13088a4d0f14d7dfa9b9dfb188cab72d6f942e186a4353b6a29c8bf
  languageName: node
  linkType: hard

"prompts@npm:^2.3.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"prop-types@npm:15.8.1, prop-types@npm:^15.5.10, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"pure-rand@npm:^7.0.0":
  version: 7.0.1
  resolution: "pure-rand@npm:7.0.1"
  checksum: 10c0/9cade41030f5ec95f5d55a11a71404cd6f46b69becaad892097cd7f58e2c6248cd0a933349ca7d21336ab629f1da42ffe899699b671bc4651600eaf6e57f837e
  languageName: node
  linkType: hard

"qrcode-terminal@npm:0.11.0":
  version: 0.11.0
  resolution: "qrcode-terminal@npm:0.11.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: 10c0/7561a649d21d7672d451ada5f2a2b393f586627cea75670c97141dc2b4b4145db547e1fddf512a3552e7fb54de530d513a736cd604c840adb908ed03c32312ad
  languageName: node
  linkType: hard

"query-string@npm:^7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10c0/a896c08e9e0d4f8ffd89a572d11f668c8d0f7df9c27c6f49b92ab31366d3ba0e9c331b9a620ee747893436cd1f2f821a6327e2bc9776bde2402ac6c270b801b2
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"rc@npm:~1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"react-devtools-core@npm:^6.1.1":
  version: 6.1.2
  resolution: "react-devtools-core@npm:6.1.2"
  dependencies:
    shell-quote: "npm:^1.6.1"
    ws: "npm:^7"
  checksum: 10c0/a038414d69eb4d1d6303a1fc2ab0f5c2350ffa5fd8d5083ba6a83545273a8d596322c6f101c7091a2c123c47b429a91b3ea65d1291581d5bc9dd58afc62270dd
  languageName: node
  linkType: hard

"react-dom@npm:19.0.0":
  version: 19.0.0
  resolution: "react-dom@npm:19.0.0"
  dependencies:
    scheduler: "npm:^0.25.0"
  peerDependencies:
    react: ^19.0.0
  checksum: 10c0/a36ce7ab507b237ae2759c984cdaad4af4096d8199fb65b3815c16825e5cfeb7293da790a3fc2184b52bfba7ba3ff31c058c01947aff6fd1a3701632aabaa6a9
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.2.2":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 10c0/0bbd2f3eb41ab2ff7380daaa55105db698d965c396df73e6874831dbafec8c4b5b08ba36ff09df01526caa3c61595247e3269558c284e37646241cba2b90a367
  languageName: node
  linkType: hard

"react-freeze@npm:^1.0.0":
  version: 1.0.4
  resolution: "react-freeze@npm:1.0.4"
  peerDependencies:
    react: ">=17.0.0"
  checksum: 10c0/8f51257c261bfefff86f618e958683536248f708019632d309ee5ebdd52f25d3c130660d06fb6f0f4fdef79f00f8ec7177233a872c2321f7d46b7e77ccc522a1
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.58.1":
  version: 7.58.1
  resolution: "react-hook-form@npm:7.58.1"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 10c0/981b9982b7eb497c3afff86c219175fdd4d92a24e3533518035239545dda23f5ebc6ba647fb29a250f768bdf811ba413094ce0103fbf36b436737fc18aa1bb49
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0, react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"react-is@npm:^19.1.0":
  version: 19.1.0
  resolution: "react-is@npm:19.1.0"
  checksum: 10c0/b6c6cadd172d5d39f66d493700d137a5545c294a62ce0f8ec793d59794c97d2bed6bad227626f16bd0e90004ed7fdc8ed662a004e6edcf5d2b7ecb6e3040ea6b
  languageName: node
  linkType: hard

"react-native-calendars@npm:^1.1313.0":
  version: 1.1313.0
  resolution: "react-native-calendars@npm:1.1313.0"
  dependencies:
    hoist-non-react-statics: "npm:^3.3.1"
    lodash: "npm:^4.17.15"
    memoize-one: "npm:^5.2.1"
    moment: "npm:^2.29.4"
    prop-types: "npm:^15.5.10"
    react-native-safe-area-context: "npm:4.5.0"
    react-native-swipe-gestures: "npm:^1.0.5"
    recyclerlistview: "npm:^4.0.0"
    xdate: "npm:^0.8.0"
  dependenciesMeta:
    moment:
      optional: true
  checksum: 10c0/a1c7325755ebac65bfed8224c1308ab430fa5dea43c95fed027eb23745b075f6ec1906cce18d95a3f5dcd127860aebebddb3a040408a281071e3f6d0cd19c5b9
  languageName: node
  linkType: hard

"react-native-crypto-js@npm:^1.0.0":
  version: 1.0.0
  resolution: "react-native-crypto-js@npm:1.0.0"
  checksum: 10c0/7ba0f3ac67d09703c88536dff6edebdb9fc3b19f8d3cced85e0a391d01c0b7293d8a34552d51ec97361b3367ec763fa1318412e1b8e7eb801244e4063d365796
  languageName: node
  linkType: hard

"react-native-css-interop@npm:0.1.22":
  version: 0.1.22
  resolution: "react-native-css-interop@npm:0.1.22"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/traverse": "npm:^7.23.0"
    "@babel/types": "npm:^7.23.0"
    debug: "npm:^4.3.7"
    lightningcss: "npm:^1.27.0"
    semver: "npm:^7.6.3"
  peerDependencies:
    react: ">=18"
    react-native: "*"
    react-native-reanimated: ">=3.6.2"
    tailwindcss: ~3
  peerDependenciesMeta:
    react-native-safe-area-context:
      optional: true
    react-native-svg:
      optional: true
  checksum: 10c0/d6052499b8615b58e02ee43b7d2906fd2b5ef90410c26af94dd11d6d93ed116ab0c96f7228573734030f1d41b4e8e2878a63b2223eb86c797730df207ed0c2f0
  languageName: node
  linkType: hard

"react-native-dotenv@npm:^3.4.11":
  version: 3.4.11
  resolution: "react-native-dotenv@npm:3.4.11"
  dependencies:
    dotenv: "npm:^16.4.5"
  peerDependencies:
    "@babel/runtime": ^7.20.6
  checksum: 10c0/9ed4a37fb59030706d17a1cc49d05af9d8fc57dcb2ef97e122b844f3ab6f6331ebc089afac559fe5bddf37806aa282b4118005f1b27aae5e37fdc3fc8bd7625c
  languageName: node
  linkType: hard

"react-native-drawer-layout@npm:^4.1.11":
  version: 4.1.11
  resolution: "react-native-drawer-layout@npm:4.1.11"
  dependencies:
    use-latest-callback: "npm:^0.2.4"
  peerDependencies:
    react: ">= 18.2.0"
    react-native: "*"
    react-native-gesture-handler: ">= 2.0.0"
    react-native-reanimated: ">= 2.0.0"
  checksum: 10c0/5e7d0ce0111986c7cb6c5da6612c2454dd83d30132e39d92de364f96d53e3a15855d3c57fe2e216b1b155304fc3f57493038ecd9b28a2b5cd4771dfec0e59ae0
  languageName: node
  linkType: hard

"react-native-edge-to-edge@npm:1.6.0":
  version: 1.6.0
  resolution: "react-native-edge-to-edge@npm:1.6.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/6373cc1b447eae31689a9b62e38b15621e9273626e2324700c4c3eb58c02ce489236a4b9e3e0dc1187e062defd8316195c5b1213facd718706b79b92127a05a3
  languageName: node
  linkType: hard

"react-native-gesture-handler@npm:~2.24.0":
  version: 2.24.0
  resolution: "react-native-gesture-handler@npm:2.24.0"
  dependencies:
    "@egjs/hammerjs": "npm:^2.0.17"
    hoist-non-react-statics: "npm:^3.3.0"
    invariant: "npm:^2.2.4"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/eb2c5cb53690ae5de1482370a156cbd775f6b3054540cd47310ec4712df83a280fe7b6259f372eec4c14a6d7f70ab18f1919a9fe63beaca9ceae126edbe32298
  languageName: node
  linkType: hard

"react-native-gifted-charts@npm:^1.4.61":
  version: 1.4.61
  resolution: "react-native-gifted-charts@npm:1.4.61"
  dependencies:
    gifted-charts-core: "npm:0.1.63"
  peerDependencies:
    expo-linear-gradient: "*"
    react: "*"
    react-native: "*"
    react-native-linear-gradient: "*"
    react-native-svg: "*"
  peerDependenciesMeta:
    expo-linear-gradient:
      optional: true
    react-native-linear-gradient:
      optional: true
  checksum: 10c0/0b608502c156d4aa4f18edc5cdeb4c079f5a745fc90a2b3a738e665dea75645f32e6d49d01af817bbd33e1a2150c8da557392c4866a52ea306389302edf1958e
  languageName: node
  linkType: hard

"react-native-is-edge-to-edge@npm:1.1.7, react-native-is-edge-to-edge@npm:^1.1.6, react-native-is-edge-to-edge@npm:^1.1.7":
  version: 1.1.7
  resolution: "react-native-is-edge-to-edge@npm:1.1.7"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/b7a37437f439b1e27a4d980de01994aa71b9091dc3ed00c21172d5505fb11978cd5ed3a43f97c89d502a3a08cf26e5cea6435b8d6e93d3557a92dd43563f7021
  languageName: node
  linkType: hard

"react-native-lightbox@npm:^0.7.0":
  version: 0.7.0
  resolution: "react-native-lightbox@npm:0.7.0"
  dependencies:
    prop-types: "npm:^15.5.10"
  checksum: 10c0/f1953001c4751975cdad8104a502636aafbcc297caa3cd71b1a301e18ab848f393e57bc28f43b1594a177c33bdd3f6965d2f5747e493aee06e8b3a8ce7842884
  languageName: node
  linkType: hard

"react-native-markdown-package@npm:^1.8.2":
  version: 1.8.2
  resolution: "react-native-markdown-package@npm:1.8.2"
  dependencies:
    lodash: "npm:^4.17.15"
    react-native-lightbox: "npm:^0.7.0"
    simple-markdown: "npm:^0.7.1"
  checksum: 10c0/ac57883a4616ecaf396a98a5b24e24ab7f1666d8b9585b2cfafb8ab8839b9756e35a2503cd78d495bc799061d0491fc514b9f3c9eea08bbaf968cda9865c380a
  languageName: node
  linkType: hard

"react-native-paper@npm:^5.14.5":
  version: 5.14.5
  resolution: "react-native-paper@npm:5.14.5"
  dependencies:
    "@callstack/react-theme-provider": "npm:^3.0.9"
    color: "npm:^3.1.2"
    use-latest-callback: "npm:^0.2.3"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-safe-area-context: "*"
  checksum: 10c0/acebe9b9ccdc7d9844114defb9f9e3da0930eba0f2ec3fd6c17636ef01bb20aee59d4c5e86c7624e64ac20b0abf336912a8d19c8eafa3df83266572b03e418cd
  languageName: node
  linkType: hard

"react-native-reanimated@npm:~3.17.4":
  version: 3.17.5
  resolution: "react-native-reanimated@npm:3.17.5"
  dependencies:
    "@babel/plugin-transform-arrow-functions": "npm:^7.0.0-0"
    "@babel/plugin-transform-class-properties": "npm:^7.0.0-0"
    "@babel/plugin-transform-classes": "npm:^7.0.0-0"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.0.0-0"
    "@babel/plugin-transform-optional-chaining": "npm:^7.0.0-0"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.0.0-0"
    "@babel/plugin-transform-template-literals": "npm:^7.0.0-0"
    "@babel/plugin-transform-unicode-regex": "npm:^7.0.0-0"
    "@babel/preset-typescript": "npm:^7.16.7"
    convert-source-map: "npm:^2.0.0"
    invariant: "npm:^2.2.4"
    react-native-is-edge-to-edge: "npm:1.1.7"
  peerDependencies:
    "@babel/core": ^7.0.0-0
    react: "*"
    react-native: "*"
  checksum: 10c0/22788541546cf3e818f0ad9fc9fb1cb53fd7b398d5f49078cd6adf8064957663d97de4e60de9e7894a359d2379685a9dd5d69183c3e13b5e4e78f2d49333921a
  languageName: node
  linkType: hard

"react-native-safe-area-context@npm:4.5.0":
  version: 4.5.0
  resolution: "react-native-safe-area-context@npm:4.5.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/cd9dfe25803b7b120940c243d9c9f10b0b61d262ad5875245909cdbf0025684241189b2796d35028519fee0e7a94e6f47bcaf2e23fc7801875d5f633a7778296
  languageName: node
  linkType: hard

"react-native-safe-area-context@npm:5.4.0":
  version: 5.4.0
  resolution: "react-native-safe-area-context@npm:5.4.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/729fef1f768d57b905f51882374aa93b209d54576b8a0cf328e0a349c8dc9705ae8f9032e572fd7a7c9e94b588105f44760c0bb15ab9911b7977073d6754b54d
  languageName: node
  linkType: hard

"react-native-screens@npm:^4.11.1":
  version: 4.11.1
  resolution: "react-native-screens@npm:4.11.1"
  dependencies:
    react-freeze: "npm:^1.0.0"
    react-native-is-edge-to-edge: "npm:^1.1.7"
    warn-once: "npm:^0.1.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/88a33ba419bd571cc318e80d25eb172f5829677f2dd80dcb69cbeaa6a35ba26214e0e82af87baa375182afe41a276e8ef1a9d13b826f662f3a389982492c2879
  languageName: node
  linkType: hard

"react-native-svg@npm:15.11.2":
  version: 15.11.2
  resolution: "react-native-svg@npm:15.11.2"
  dependencies:
    css-select: "npm:^5.1.0"
    css-tree: "npm:^1.1.3"
    warn-once: "npm:0.1.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/040f3a298db80f4a282f9616c9550d7978beebc518de523eb417106afd7f5cb1d021424ea287cedba705f0c48aa15631056c42a79c4b46edcafa34cba71aed63
  languageName: node
  linkType: hard

"react-native-swipe-gestures@npm:^1.0.5":
  version: 1.0.5
  resolution: "react-native-swipe-gestures@npm:1.0.5"
  checksum: 10c0/33bb8bd402de82ed751a8f1913fe7e02ddc533edb7178bf9e499387b93d0d14154cef120f273c554bb988ad1ba87165b80be213611cb92af8875f2380ec7878b
  languageName: node
  linkType: hard

"react-native-toast-message@npm:^2.3.3":
  version: 2.3.3
  resolution: "react-native-toast-message@npm:2.3.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/10c7cad8ce287786c555c032f03040ecbaeaa920f44b08161364408ccd18bd0fc3a3e6622f80724ad2c0c7366f667e0d946317045aec08d934ca73358811f782
  languageName: node
  linkType: hard

"react-native-vector-icons@npm:^10.2.0":
  version: 10.2.0
  resolution: "react-native-vector-icons@npm:10.2.0"
  dependencies:
    prop-types: "npm:^15.7.2"
    yargs: "npm:^16.1.1"
  bin:
    fa-upgrade.sh: bin/fa-upgrade.sh
    fa5-upgrade: bin/fa5-upgrade.sh
    fa6-upgrade: bin/fa6-upgrade.sh
    generate-icon: bin/generate-icon.js
  checksum: 10c0/76a7f327b40b29e567f48a49ebc3ad87b755d4e431d66fec3566dfe5e673084cee9b1663f10903a6572aab31c7bcec45100172afb1d46055dfacbbe46b92b348
  languageName: node
  linkType: hard

"react-native-web@npm:^0.20.0":
  version: 0.20.0
  resolution: "react-native-web@npm:0.20.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.6"
    "@react-native/normalize-colors": "npm:^0.74.1"
    fbjs: "npm:^3.0.4"
    inline-style-prefixer: "npm:^7.0.1"
    memoize-one: "npm:^6.0.0"
    nullthrows: "npm:^1.1.1"
    postcss-value-parser: "npm:^4.2.0"
    styleq: "npm:^0.1.3"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  checksum: 10c0/266c16c67ccc4114864cf4facac14c3736412c937af8cf031eaaa618e801723f2c4aac5bf2d680536bbbe95602b97c13a819e775602884e900dd1362bbe2f3f5
  languageName: node
  linkType: hard

"react-native-webview@npm:13.13.5":
  version: 13.13.5
  resolution: "react-native-webview@npm:13.13.5"
  dependencies:
    escape-string-regexp: "npm:^4.0.0"
    invariant: "npm:2.2.4"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/187441eac5a747acb58ae388b07611fcb13c6c8c801b7e3fc5175ea46e20cbc28db38ce777459fa4f405a3b4703e3011cd04c9218ac4a088a8a06031a8a2629c
  languageName: node
  linkType: hard

"react-native@npm:0.79.5":
  version: 0.79.5
  resolution: "react-native@npm:0.79.5"
  dependencies:
    "@jest/create-cache-key-function": "npm:^29.7.0"
    "@react-native/assets-registry": "npm:0.79.5"
    "@react-native/codegen": "npm:0.79.5"
    "@react-native/community-cli-plugin": "npm:0.79.5"
    "@react-native/gradle-plugin": "npm:0.79.5"
    "@react-native/js-polyfills": "npm:0.79.5"
    "@react-native/normalize-colors": "npm:0.79.5"
    "@react-native/virtualized-lists": "npm:0.79.5"
    abort-controller: "npm:^3.0.0"
    anser: "npm:^1.4.9"
    ansi-regex: "npm:^5.0.0"
    babel-jest: "npm:^29.7.0"
    babel-plugin-syntax-hermes-parser: "npm:0.25.1"
    base64-js: "npm:^1.5.1"
    chalk: "npm:^4.0.0"
    commander: "npm:^12.0.0"
    event-target-shim: "npm:^5.0.1"
    flow-enums-runtime: "npm:^0.0.6"
    glob: "npm:^7.1.1"
    invariant: "npm:^2.2.4"
    jest-environment-node: "npm:^29.7.0"
    memoize-one: "npm:^5.0.0"
    metro-runtime: "npm:^0.82.0"
    metro-source-map: "npm:^0.82.0"
    nullthrows: "npm:^1.1.1"
    pretty-format: "npm:^29.7.0"
    promise: "npm:^8.3.0"
    react-devtools-core: "npm:^6.1.1"
    react-refresh: "npm:^0.14.0"
    regenerator-runtime: "npm:^0.13.2"
    scheduler: "npm:0.25.0"
    semver: "npm:^7.1.3"
    stacktrace-parser: "npm:^0.1.10"
    whatwg-fetch: "npm:^3.0.0"
    ws: "npm:^6.2.3"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@types/react": ^19.0.0
    react: ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  bin:
    react-native: cli.js
  checksum: 10c0/e5398a99d01c8e4f016ea1ae2c5d51a2d5ae38a9e1edc6f66f562ec178293e025a1fb93d14163a38033d9bc1fae8dad01d5611127a9ed0019cf3cbc9618f4807
  languageName: node
  linkType: hard

"react-redux@npm:^9.2.0":
  version: 9.2.0
  resolution: "react-redux@npm:9.2.0"
  dependencies:
    "@types/use-sync-external-store": "npm:^0.0.6"
    use-sync-external-store: "npm:^1.4.0"
  peerDependencies:
    "@types/react": ^18.2.25 || ^19
    react: ^18.0 || ^19
    redux: ^5.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    redux:
      optional: true
  checksum: 10c0/00d485f9d9219ca1507b4d30dde5f6ff8fb68ba642458f742e0ec83af052f89e65cd668249b99299e1053cc6ad3d2d8ac6cb89e2f70d2ac5585ae0d7fa0ef259
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.0, react-refresh@npm:^0.14.2":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: 10c0/875b72ef56b147a131e33f2abd6ec059d1989854b3ff438898e4f9310bfcc73acff709445b7ba843318a953cb9424bcc2c05af2b3d80011cee28f25aef3e2ebb
  languageName: node
  linkType: hard

"react-test-renderer@npm:^19.1.0":
  version: 19.1.0
  resolution: "react-test-renderer@npm:19.1.0"
  dependencies:
    react-is: "npm:^19.1.0"
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.0
  checksum: 10c0/34ed4a37ba8b0beb96c048de6ff28574f018a18dd1042c24f8f46142d48eb5b27f82ff7c2823d082932fd3983c5a3529ab8cc8f15191d4306df0082f9f84678f
  languageName: node
  linkType: hard

"react@npm:19.0.0":
  version: 19.0.0
  resolution: "react@npm:19.0.0"
  checksum: 10c0/9cad8f103e8e3a16d15cb18a0d8115d8bd9f9e1ce3420310aea381eb42aa0a4f812cf047bb5441349257a05fba8a291515691e3cb51267279b2d2c3253f38471
  languageName: node
  linkType: hard

"reactotron-core-client@npm:2.9.7, reactotron-core-client@npm:^2.9.7":
  version: 2.9.7
  resolution: "reactotron-core-client@npm:2.9.7"
  dependencies:
    reactotron-core-contract: "npm:0.2.5"
  checksum: 10c0/c18f93761feeadd043461b59ad0dee748d8a6ab0ac01beecda9653fdd190e15ecccd55d246a8ac25058f2b74b4d62ff07861f3fb0856d1995418b45886170485
  languageName: node
  linkType: hard

"reactotron-core-contract@npm:0.2.5":
  version: 0.2.5
  resolution: "reactotron-core-contract@npm:0.2.5"
  checksum: 10c0/4cf35d9f5e442bbbd32346f9b9ae26e4dd2400ffb9abc618d039df3b3398eaef1fb59c9df04703d6da4e2786f2922454a454c5522c055c8300146da3d6754508
  languageName: node
  linkType: hard

"reactotron-react-native@npm:^5.1.13":
  version: 5.1.13
  resolution: "reactotron-react-native@npm:5.1.13"
  dependencies:
    mitt: "npm:^3.0.1"
    reactotron-core-client: "npm:2.9.7"
  peerDependencies:
    react-native: ">=0.40.0"
  checksum: 10c0/94e9ce409bba754f09df09cc3a6dbc8ad343de34a5b89b02eb423ef76a251894625db53f3c6a0f9c6e973a1c2339dab7af81f609cbe7d26eccedcbfcda80d441
  languageName: node
  linkType: hard

"reactotron-redux@npm:^3.2.0":
  version: 3.2.0
  resolution: "reactotron-redux@npm:3.2.0"
  dependencies:
    microdiff: "npm:^1.5.0"
  peerDependencies:
    reactotron-core-client: "*"
    redux: ">=4.0.0"
  checksum: 10c0/1c28f7630635351c78daae5864928d1cd54d59095605673d5cffc8c145ecf936605ae16de77dec400a3b74d8ebffffa837ce286d96bfb0ac3693e0a86e3fa62d
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: "npm:^2.3.0"
  checksum: 10c0/90cb2750213c7dd7c80cb420654344a311fdec12944e81eb912cd82f1bc92aea21885fa6ce442e3336d9fccd663b8a7a19c46d9698e6ca55620848ab932da814
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"recyclerlistview@npm:^4.0.0":
  version: 4.2.3
  resolution: "recyclerlistview@npm:4.2.3"
  dependencies:
    lodash.debounce: "npm:4.0.8"
    prop-types: "npm:15.8.1"
    ts-object-utils: "npm:0.0.5"
  peerDependencies:
    react: ">= 15.2.1"
    react-native: ">= 0.30.0"
  checksum: 10c0/40b8b948d09f560ce98842e9141d2ce7ee5677ae2c20f7e30de901136d7764a42cc4e5ba3242fca0934055d5fef99c5b03430fcba28d7cfc27155b4ef97f70d5
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: "npm:^4.0.0"
    strip-indent: "npm:^3.0.0"
  checksum: 10c0/d64a6b5c0b50eb3ddce3ab770f866658a2b9998c678f797919ceb1b586bab9259b311407280bd80b804e2a7c7539b19238ae6a2a20c843f1a7fcff21d48c2eae
  languageName: node
  linkType: hard

"redux-persist@npm:^6.0.0":
  version: 6.0.0
  resolution: "redux-persist@npm:6.0.0"
  peerDependencies:
    redux: ">4.0.0"
  checksum: 10c0/8242d265ab8d28bbc95cf2dc2a05b869eb67aa309b1ed08163c926f3af56dd8eb1ea62118286083461b8ef2024d3b349fd264e5a62a70eb2e74d068c832d5bf2
  languageName: node
  linkType: hard

"redux-thunk@npm:^3.1.0":
  version: 3.1.0
  resolution: "redux-thunk@npm:3.1.0"
  peerDependencies:
    redux: ^5.0.0
  checksum: 10c0/21557f6a30e1b2e3e470933247e51749be7f1d5a9620069a3125778675ce4d178d84bdee3e2a0903427a5c429e3aeec6d4df57897faf93eb83455bc1ef7b66fd
  languageName: node
  linkType: hard

"redux@npm:^5.0.1":
  version: 5.0.1
  resolution: "redux@npm:5.0.1"
  checksum: 10c0/b10c28357194f38e7d53b760ed5e64faa317cc63de1fb95bc5d9e127fab956392344368c357b8e7a9bedb0c35b111e7efa522210cfdc3b3c75e5074718e9069c
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.2":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10c0/87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"requireg@npm:^0.2.2":
  version: 0.2.2
  resolution: "requireg@npm:0.2.2"
  dependencies:
    nested-error-stacks: "npm:~2.0.1"
    rc: "npm:~1.2.7"
    resolve: "npm:~1.7.1"
  checksum: 10c0/806cff08d8fa63f2ec9c74fa9602c86b56627a824d0a188bf777c8d82ba012a1b3c01ab6e88ffcf610713b6bc5ec8a9f9e55dc941b7606ce735e72c4d9daa059
  languageName: node
  linkType: hard

"reselect@npm:^5.1.0":
  version: 5.1.1
  resolution: "reselect@npm:5.1.1"
  checksum: 10c0/219c30da122980f61853db3aebd173524a2accd4b3baec770e3d51941426c87648a125ca08d8c57daa6b8b086f2fdd2703cb035dd6231db98cdbe1176a71f489
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/e608a3ebd15356264653c32d7ecbc8fd702f94c6703ea4ac2fb81d9c359180cba0ae2e6b71faa446631ed6145454d5a56b227efc33a2d40638ac13f8beb20ee4
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: 10c0/24affcf8e81f4c62f0dcabc774afe0e19c1f38e34e43daac0ddb409d79435fc3037f612b0cc129178b8c220442c3babd673e88e870d27215c99454566e770ebc
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10c0/fb8f7bbe2ca281a73b7ef423a1cbc786fb244bd7a95cbe5c3fba25b27d327150beca8ba02f622baea65919a57e061eb5005204daa5f93ed590d9b77463a567ab
  languageName: node
  linkType: hard

"resolve-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-workspace-root@npm:2.0.0"
  checksum: 10c0/658e6fbc199c51f4903867ab371f03122d9865b4fb4fd3a2069c39b429132d91535e5112f5c6c561fa0852cb8393505b7f94b58c3e2566bab610a48172f38e3f
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.3":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10c0/1ade1493f4642a6267d0a5e68faeac20b3d220f18c28b140343feb83694d8fed7a286852aef43689d16042c61e2ddb270be6578ad4a13990769e12065191200d
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.14.2, resolve@npm:^1.22.2, resolve@npm:^1.22.4, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/a6c33555e3482ea2ec4c6e3d3bf0d78128abf69dca99ae468e64f1e30acaa318fd267fb66c8836b04d558d3e2d6ed875fe388067e7d8e0de647d3c21af21c43a
  languageName: node
  linkType: hard

"resolve@npm:~1.7.1":
  version: 1.7.1
  resolution: "resolve@npm:1.7.1"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/6e9e29185ac57801aff013849e9717c769ef0a27eac30b6492405ba3d61db73d8967023b96578f4b2deba4ef5fb11fc4f0a4db47c0f536890ced5c014e94fbde
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.7#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.8#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.5#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/78ad6edb8309a2bfb720c2c1898f7907a37f858866ce11a5974643af1203a6a6e05b2fa9c53d8064a673a447b83d42569260c306d43628bff5bb101969708355
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A~1.7.1#optional!builtin<compat/resolve>":
  version: 1.7.1
  resolution: "resolve@patch:resolve@npm%3A1.7.1#optional!builtin<compat/resolve>::version=1.7.1&hash=3bafbf"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/1301dba7c12cd9dab2ab4eee8518089f25bb7480db34b746a923ded472c4c0600ebb1ba9b8028ca843f7c6017ac76524355800c52b82633e53bd601ca288b4de
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: "npm:^2.0.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/f5b335bee06f440445e976a7031a3ef53691f9b7c4a9d42a469a0edaf8a5508158a0d561ff2b26a1f4f38783bcca2c0e5c3a44f927326f6694d5b44d7a4993e6
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10c0/6bf86318a254c5d898ede6bd3ded15daf68ae08a5495a2739564eb265cd13bcc64a07ab466fb204f67ce472bb534eb8612dac587435515169593f4fffa11de7c
  languageName: node
  linkType: hard

"scheduler@npm:0.25.0, scheduler@npm:^0.25.0":
  version: 0.25.0
  resolution: "scheduler@npm:0.25.0"
  checksum: 10c0/a4bb1da406b613ce72c1299db43759526058fdcc413999c3c3e0db8956df7633acf395cb20eb2303b6a65d658d66b6585d344460abaee8080b4aa931f10eaafe
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10c0/5b8d5bfddaae3513410eda54f2268e98a376a429931921a81b5c3a2873aab7ca4d775a8caac5498f8cbc7d0daeab947cf923dbd8e215d61671f9f4e392d34356
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.1":
  version: 4.3.2
  resolution: "schema-utils@npm:4.3.2"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10c0/981632f9bf59f35b15a9bcdac671dd183f4946fe4b055ae71a301e66a9797b95e5dd450de581eb6cca56fb6583ce8f24d67b2d9f8e1b2936612209697f6c277e
  languageName: node
  linkType: hard

"school-app@workspace:.":
  version: 0.0.0-use.local
  resolution: "school-app@workspace:."
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/runtime": "npm:^7.27.6"
    "@expo-google-fonts/inter": "npm:^0.4.1"
    "@expo/vector-icons": "npm:^14.1.0"
    "@gorhom/bottom-sheet": "npm:^5.1.6"
    "@hookform/resolvers": "npm:^5.1.1"
    "@react-native-async-storage/async-storage": "npm:2.1.2"
    "@react-native-community/blur": "npm:^4.4.1"
    "@react-native-community/datetimepicker": "npm:8.4.1"
    "@react-native-community/netinfo": "npm:^11.4.1"
    "@react-native-picker/picker": "npm:^2.11.1"
    "@react-navigation/bottom-tabs": "npm:^7.4.2"
    "@react-navigation/drawer": "npm:^7.3.9"
    "@react-navigation/native": "npm:^7.1.14"
    "@react-navigation/stack": "npm:^7.4.2"
    "@reduxjs/toolkit": "npm:^2.8.2"
    "@rn-primitives/avatar": "npm:^1.2.0"
    "@rn-primitives/slot": "npm:^1.2.0"
    "@tabler/icons-react-native": "npm:^3.34.0"
    "@testing-library/react-native": "npm:^13.2.0"
    "@types/jest": "npm:^30.0.0"
    "@types/react": "npm:~19.0.10"
    "@types/react-native-crypto-js": "npm:^1"
    "@types/react-test-renderer": "npm:^19"
    axios: "npm:^1.10.0"
    babel-jest: "npm:^30.0.4"
    class-variance-authority: "npm:^0.7.1"
    clsx: "npm:^2.1.1"
    dotenv: "npm:^17.2.0"
    eslint: "npm:^9.0.0"
    eslint-config-expo: "npm:~9.2.0"
    eslint-config-prettier: "npm:^10.1.3"
    eslint-plugin-prettier: "npm:^5.4.0"
    expo: "npm:53.0.19"
    expo-application: "npm:^6.1.5"
    expo-audio: "npm:^0.4.8"
    expo-blur: "npm:^14.1.5"
    expo-constants: "npm:~17.1.7"
    expo-device: "npm:^7.1.4"
    expo-font: "npm:^13.3.2"
    expo-image: "npm:2.3.2"
    expo-linear-gradient: "npm:~14.1.5"
    expo-linking: "npm:~7.1.7"
    expo-navigation-bar: "npm:4.2.7"
    expo-router: "npm:~5.1.3"
    expo-screen-capture: "npm:^7.1.5"
    expo-splash-screen: "npm:~0.30.10"
    expo-status-bar: "npm:~2.2.3"
    expo-video: "npm:^2.2.2"
    jest: "npm:^30.0.4"
    lottie-react-native: "npm:7.2.2"
    lucide-react-native: "npm:^0.514.0"
    nativewind: "npm:^4.1.23"
    prettier: "npm:^3.5.3"
    react: "npm:19.0.0"
    react-dom: "npm:19.0.0"
    react-hook-form: "npm:^7.58.1"
    react-native: "npm:0.79.5"
    react-native-calendars: "npm:^1.1313.0"
    react-native-crypto-js: "npm:^1.0.0"
    react-native-dotenv: "npm:^3.4.11"
    react-native-drawer-layout: "npm:^4.1.11"
    react-native-gesture-handler: "npm:~2.24.0"
    react-native-gifted-charts: "npm:^1.4.61"
    react-native-markdown-package: "npm:^1.8.2"
    react-native-paper: "npm:^5.14.5"
    react-native-reanimated: "npm:~3.17.4"
    react-native-safe-area-context: "npm:5.4.0"
    react-native-screens: "npm:^4.11.1"
    react-native-svg: "npm:15.11.2"
    react-native-toast-message: "npm:^2.3.3"
    react-native-vector-icons: "npm:^10.2.0"
    react-native-web: "npm:^0.20.0"
    react-native-webview: "npm:13.13.5"
    react-redux: "npm:^9.2.0"
    react-test-renderer: "npm:^19.1.0"
    reactotron-core-client: "npm:^2.9.7"
    reactotron-react-native: "npm:^5.1.13"
    reactotron-redux: "npm:^3.2.0"
    redux: "npm:^5.0.1"
    redux-persist: "npm:^6.0.0"
    tailwind-merge: "npm:^3.0.1"
    tailwindcss: "npm:^3"
    tailwindcss-animate: "npm:^1.0.7"
    typescript: "npm:~5.8.3"
    zod: "npm:^3.25.67"
  languageName: unknown
  linkType: soft

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.1.3, semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3, semver@npm:^7.7.1, semver@npm:^7.7.2":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"semver@npm:~7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10c0/88f33e148b210c153873cb08cfe1e281d518aaa9a666d4d148add6560db5cd3c582f3a08ccb91f38d5f379ead256da9931234ed122057f40bb5766e65e58adaf
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"send@npm:^0.19.0":
  version: 0.19.1
  resolution: "send@npm:0.19.1"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ceb859859822bf55e705b96db9a909870626d1a6bfcf62a88648b9681048a7840c0ff1f4afd7babea4ccfabff7d64a7dda68a6f6c63c255cc83f40a412a1db8e
  languageName: node
  linkType: hard

"serialize-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "serialize-error@npm:2.1.0"
  checksum: 10c0/919c40d293cd36b16bb3fce38a3a460e0c51a34cf0ee59815bbeec7c48ffe0a66ea2dec08aa5340ef6dfc1f22e7317f6e1ed76cdbb2ec3c494c0c4debfb344f8
  languageName: node
  linkType: hard

"serve-static@npm:^1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"server-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "server-only@npm:0.0.1"
  checksum: 10c0/4704f0ef85da0be981af6d4ed8e739d39bcfd265b9c246a684060acda5642d0fdc6daffc2308e71e2682c5f508090978802eae0a77623c9b90a49f9ae68048d6
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10c0/b926efb51cd0f47aa9bc061add788a4a650550bbe50647962113a4579b60af2abe7b62f9b02314acc6f97151d4cf87033a2b15fc20852fae306d1a095215396c
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 10c0/bee87c34e1e986cfb4c30846b8e6327d18874f10b535699866f368ade11ea4ee45433d97bf5eada22c4320c27df79c3a6a7eb1bf3ecfc47f2c997d9e5e2672fd
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-markdown@npm:^0.7.1":
  version: 0.7.3
  resolution: "simple-markdown@npm:0.7.3"
  dependencies:
    "@types/react": "npm:>=16.0.0"
  checksum: 10c0/f79d81cf25b3be67238509a4f764b14c7e4a20c3a2e6e4c4d1a747b2c72812339ed1a8bf375f281f69faede3056d46b6aa82f0d2aba4b508e2220aa8607ed9a3
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.4.0
  resolution: "simple-plist@npm:1.4.0"
  dependencies:
    bplist-creator: "npm:0.1.1"
    bplist-parser: "npm:0.3.2"
    plist: "npm:^3.0.5"
  checksum: 10c0/226c283492d8518d715e4133d94bdbd15c0619561bcde583b4807b36cde106c0078c615b9b4e25c0e8758a4ae4e79ed5dd76e57cd528d8b7001ecab5ad35e343
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slugify@npm:^1.3.4, slugify@npm:^1.6.6":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 10c0/e7e63f08f389a371d6228bc19d64ec84360bf0a538333446cc49dbbf3971751a6d180d2f31551188dd007a65ca771e69f574e0283290a7825a818e90b75ef44d
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/e427d0eb0451cfd04e20b9156ea8c0e9b5e38a8d70f21e55c30fbe4214eda37cfc25d782c63f9adc5fbdad6d062a0f127ef2cefc9a44b6fee2b9ea5d1ed10827
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/137539f8c453fa0f496ea42049ab5da4569f96781f6ac8e5bfda26937be9494f4e8891f523c5f98f0e85f71b35d74127a00c46f83f6a4f54672b58d53202565e
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20, source-map-support@npm:~0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10c0/56df8344f5a5de8521898a5c090023df1d8b8c75be6228f56c52491e0fc1617a5236f2ac3a066adb67a73231eac216ccea7b5b4a2423a543c277cb2f48d24c29
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.5":
  version: 0.0.5
  resolution: "stable-hash@npm:0.0.5"
  checksum: 10c0/ca670cb6d172f1c834950e4ec661e2055885df32fee3ebf3647c5df94993b7c2666a5dbc1c9a62ee11fc5c24928579ec5e81bb5ad31971d355d5a341aab493b3
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3, stack-utils@npm:^2.0.6":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10c0/651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 10c0/18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.11
  resolution: "stacktrace-parser@npm:0.1.11"
  dependencies:
    type-fest: "npm:^0.7.1"
  checksum: 10c0/4633d9afe8cd2f6c7fb2cebdee3cc8de7fd5f6f9736645fd08c0f66872a303061ce9cc0ccf46f4216dc94a7941b56e331012398dc0024dc25e46b5eb5d4ff018
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10c0/de4e45706bb4c0354a4b1122a2b8cc45a639e86206807ce0baf390ee9218d3ef181923fa4d2b67443367c491aa255c5fbaa64bb74648e3c5b48299928af86c09
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 10c0/14a351f0a066eaa08c8c64a74f4aedd87dd7a8e59d4be224703da33dca3eb370828ee6c0ae3fff59a9c743e8098728fc95c5f052ae7741672a31e6b1430ba50a
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10c0/010cbc78da0e2cf833b0f5dc769e21ae74cdc5d5f5bd555f14a4a4876c8ad2c85ab8b5bdf9a722dc71a11dcd3184085e1c3c0bd50ec6bb85fffc0f28cf82597d
  languageName: node
  linkType: hard

"string-length@npm:^4.0.2":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: "npm:^1.0.2"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/1cd77409c3d7db7bc59406f6bcc9ef0783671dcbabb23597a1177c166906ef2ee7c8290f78cae73a8aec858768f189d2cb417797df5e15ec4eb5e16b3346340c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/1a53328ada73f4a77f1fdf1c79414700cf718d0a8ef6672af5603e709d26a24f2181208144aed7e858b1bcc1a0d08567a570abfb45567db4ae47637ed2c2f85c
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.5"
  checksum: 10c0/94c7978566cffa1327d470fd924366438af9b04b497c43a9805e476e2e908aa37a1fd34cc0911156c17556dab62159d12c7b92b3cc304c3e1281fe4c8e668f40
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: "npm:^4.1.0"
  checksum: 10c0/de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10c0/51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10c0/26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: "npm:^1.0.0"
  checksum: 10c0/ae0deaf41c8d1001c5d4fbe16cb553865c1863da4fae036683b474fa926af9fc121e155cb3fc57a68262b2ae7d5b8420aa752c97a6428c315d00efe2a3875679
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"structured-headers@npm:^0.4.1":
  version: 0.4.1
  resolution: "structured-headers@npm:0.4.1"
  checksum: 10c0/b7d326f6fec7e7f7901d1e0542577293b5d029bf3e1fb84995e33d9aabe47d03259f64ca2d778ef5c427f6f00c78bafa051b6f233131e1556f8bb9102b11ed64
  languageName: node
  linkType: hard

"styleq@npm:^0.1.3":
  version: 0.1.3
  resolution: "styleq@npm:0.1.3"
  checksum: 10c0/975d951792e65052f1f6e41aaad46492642ce4922b3dc36d4b49b37c8509f9a776794d8f275360f00116a5e6ab1e31514bdcd5840656c4e3213da6803fa12941
  languageName: node
  linkType: hard

"sucrase@npm:3.35.0, sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    commander: "npm:^4.0.0"
    glob: "npm:^10.3.10"
    lines-and-columns: "npm:^1.1.6"
    mz: "npm:^2.7.0"
    pirates: "npm:^4.0.1"
    ts-interface-checker: "npm:^0.1.9"
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 10c0/ac85f3359d2c2ecbf5febca6a24ae9bf96c931f05fde533c22a94f59c6a74895e5d5f0e871878dfd59c2697a75ebb04e4b2224ef0bfc24ca1210735c2ec191ef
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0, supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"synckit@npm:^0.11.7, synckit@npm:^0.11.8":
  version: 0.11.8
  resolution: "synckit@npm:0.11.8"
  dependencies:
    "@pkgr/core": "npm:^0.2.4"
  checksum: 10c0/a1de5131ee527512afcaafceb2399b2f3e63678e56b831e1cb2dc7019c972a8b654703a3b94ef4166868f87eb984ea252b467c9d9e486b018ec2e6a55c24dfd8
  languageName: node
  linkType: hard

"tailwind-merge@npm:^3.0.1":
  version: 3.3.1
  resolution: "tailwind-merge@npm:3.3.1"
  checksum: 10c0/b84c6a78d4669fa12bf5ab8f0cdc4400a3ce0a7c006511af4af4be70bb664a27466dbe13ee9e3b31f50ddf6c51d380e8192ce0ec9effce23ca729d71a9f63818
  languageName: node
  linkType: hard

"tailwindcss-animate@npm:^1.0.7":
  version: 1.0.7
  resolution: "tailwindcss-animate@npm:1.0.7"
  peerDependencies:
    tailwindcss: "*"
  checksum: 10c0/ec7dbd1631076b97d66a1fbaaa06e0725fccfa63119221e8d87a997b02dcede98ad88bb1ef6665b968f5d260fcefb10592e0299ca70208d365b37761edf5e19a
  languageName: node
  linkType: hard

"tailwindcss@npm:^3":
  version: 3.4.17
  resolution: "tailwindcss@npm:3.4.17"
  dependencies:
    "@alloc/quick-lru": "npm:^5.2.0"
    arg: "npm:^5.0.2"
    chokidar: "npm:^3.6.0"
    didyoumean: "npm:^1.2.2"
    dlv: "npm:^1.1.3"
    fast-glob: "npm:^3.3.2"
    glob-parent: "npm:^6.0.2"
    is-glob: "npm:^4.0.3"
    jiti: "npm:^1.21.6"
    lilconfig: "npm:^3.1.3"
    micromatch: "npm:^4.0.8"
    normalize-path: "npm:^3.0.0"
    object-hash: "npm:^3.0.0"
    picocolors: "npm:^1.1.1"
    postcss: "npm:^8.4.47"
    postcss-import: "npm:^15.1.0"
    postcss-js: "npm:^4.0.1"
    postcss-load-config: "npm:^4.0.2"
    postcss-nested: "npm:^6.2.0"
    postcss-selector-parser: "npm:^6.1.2"
    resolve: "npm:^1.22.8"
    sucrase: "npm:^3.35.0"
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: 10c0/cc42c6e7fdf88a5507a0d7fea37f1b4122bec158977f8c017b2ae6828741f9e6f8cb90282c6bf2bd5951fd1220a53e0a50ca58f5c1c00eb7f5d9f8b80dc4523c
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"temp-dir@npm:~2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: 10c0/b1df969e3f3f7903f3426861887ed76ba3b495f63f6d0c8e1ce22588679d9384d336df6064210fda14e640ed422e2a17d5c40d901f60e161c99482d723f4d309
  languageName: node
  linkType: hard

"terminal-link@npm:^2.1.1":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    supports-hyperlinks: "npm:^2.0.0"
  checksum: 10c0/947458a5cd5408d2ffcdb14aee50bec8fb5022ae683b896b2f08ed6db7b2e7d42780d5c8b51e930e9c322bd7c7a517f4fa7c76983d0873c83245885ac5ee13e3
  languageName: node
  linkType: hard

"terser@npm:^5.15.0":
  version: 5.43.1
  resolution: "terser@npm:5.43.1"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/9cd3fa09ea6bcb79eb71995216b8bef0651b18c5c3877535fc699a77e1ab43b140a4da5811ac9baeb654fa9ec939b17324092f0f0bdb19c402e101e3db946986
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^7.1.4"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/019d33d81adff3f9f1bfcff18125fb2d3c65564f437d9be539270ee74b994986abb8260c7c2ce90e8f30162178b09dbbce33c6389273afac4f36069c48521f57
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 10c0/9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: 10c0/f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"throat@npm:^5.0.0":
  version: 5.0.0
  resolution: "throat@npm:5.0.0"
  checksum: 10c0/1b9c661dabf93ff9026fecd781ccfd9b507c41b9d5e581614884fffd09f3f9ebfe26d3be668ccf904fd324dd3f6efe1a3ec7f83e91b1dff9fdcc6b7d39b8bfe3
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.13":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10c0/f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10c0/9806a38adea2db0f6aa217ccc6bc9c391ddba338a9fe3080676d0d50ed806d305bb90e8cef0276e793d28c8a929f400abb184ddd7ff83a416959c0f4d2ce754f
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 10c0/232509f1b84192d07b81d1e9b9677088e590ac1303436da1e92b296e9be8e31ea042e3e1fd3d29b1742ad2c959e95afe30f63117b8f1bc3a3850070a5142fea7
  languageName: node
  linkType: hard

"ts-object-utils@npm:0.0.5":
  version: 0.0.5
  resolution: "ts-object-utils@npm:0.0.5"
  checksum: 10c0/0279f8a7504b3905f2b14769769985f214154f1aedc60077c3baaced078369ae465aecc6acc04c614f40893e559d05697f6f4ef9fc411e3b6d1d15e6269a5e14
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/5b4f301a2b7a3766a986baf8fc0e177eb80bdba6e396792ff92dc23b5bca8bb279fc96517dcaaef63a3b49bebc6c4c833653ec58155780bc906bdbcf7dda0ef5
  languageName: node
  linkType: hard

"tslib@npm:^2.4.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 10c0/ce6b5ef806a76bf08d0daa78d65e61f24d9a0380bd1f1df36ffb61f84d14a0985c3a921923cf4b97831278cb6fa9bf1b89c751df09407e0510b14e8c081e4e0f
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typescript@npm:~5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f8bb01196e542e64d44db3d16ee0e4063ce4f3e3966df6005f2588e86d91c03e1fb131c2581baf0fb65ee79669eea6e161cd448178986587e9f6844446dbb48
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~5.8.3#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/39117e346ff8ebd87ae1510b3a77d5d92dae5a89bde588c747d25da5c146603a99c8ee588c7ef80faaf123d89ed46f6dbd918d534d641083177d5fac38b8a1cb
  languageName: node
  linkType: hard

"ua-parser-js@npm:^0.7.33":
  version: 0.7.40
  resolution: "ua-parser-js@npm:0.7.40"
  bin:
    ua-parser-js: script/cli.js
  checksum: 10c0/d114f0b71b5b0106dcc0cb7cc26a44690073e886fa1444f8c03131d4f57b3f6645f9fb7b308b0aaaa5a2774461f9e8fe1a2a1c3ff69aa531316fcf14cd44dbe3
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.40
  resolution: "ua-parser-js@npm:1.0.40"
  bin:
    ua-parser-js: script/cli.js
  checksum: 10c0/2b6ac642c74323957dae142c31f72287f2420c12dced9603d989b96c132b80232779c429b296d7de4012ef8b64e0d8fadc53c639ef06633ce13d785a78b5be6c
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 10c0/9d9d246d1dc32f318d46116efe3cfca5a72d4f16828febc1918d94e58f6ffcf39c158aa28bf5b4fc52f410446bc7858f35151367bd7a49f21746cab6497b709b
  languageName: node
  linkType: hard

"undici@npm:^6.18.2":
  version: 6.21.3
  resolution: "undici@npm:6.21.3"
  checksum: 10c0/294da109853fad7a6ef5a172ad0ca3fb3f1f60cf34703d062a5ec967daf69ad8c03b52e6d536c5cba3bb65615769bf08e5b30798915cbccdddaca01045173dda
  languageName: node
  linkType: hard

"undici@npm:^6.18.2 || ^7.0.0":
  version: 7.10.0
  resolution: "undici@npm:7.10.0"
  checksum: 10c0/756ac876a8df845bc89eb8348c35d33a0ff63c17eb45b664075c961a7fbd4a398f94f9dce438262f55fe66e4bbb0a46aa63a3fd58ce51361c616aff11a270450
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unique-string@npm:~2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: "npm:^2.0.0"
  checksum: 10c0/11820db0a4ba069d174bedfa96c588fc2c96b083066fafa186851e563951d0de78181ac79c744c1ed28b51f9d82ac5b8196ff3e4560d0178046ef455d8c2244b
  languageName: node
  linkType: hard

"unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.6.2":
  version: 1.8.1
  resolution: "unrs-resolver@npm:1.8.1"
  dependencies:
    "@unrs/resolver-binding-darwin-arm64": "npm:1.8.1"
    "@unrs/resolver-binding-darwin-x64": "npm:1.8.1"
    "@unrs/resolver-binding-freebsd-x64": "npm:1.8.1"
    "@unrs/resolver-binding-linux-arm-gnueabihf": "npm:1.8.1"
    "@unrs/resolver-binding-linux-arm-musleabihf": "npm:1.8.1"
    "@unrs/resolver-binding-linux-arm64-gnu": "npm:1.8.1"
    "@unrs/resolver-binding-linux-arm64-musl": "npm:1.8.1"
    "@unrs/resolver-binding-linux-ppc64-gnu": "npm:1.8.1"
    "@unrs/resolver-binding-linux-riscv64-gnu": "npm:1.8.1"
    "@unrs/resolver-binding-linux-riscv64-musl": "npm:1.8.1"
    "@unrs/resolver-binding-linux-s390x-gnu": "npm:1.8.1"
    "@unrs/resolver-binding-linux-x64-gnu": "npm:1.8.1"
    "@unrs/resolver-binding-linux-x64-musl": "npm:1.8.1"
    "@unrs/resolver-binding-wasm32-wasi": "npm:1.8.1"
    "@unrs/resolver-binding-win32-arm64-msvc": "npm:1.8.1"
    "@unrs/resolver-binding-win32-ia32-msvc": "npm:1.8.1"
    "@unrs/resolver-binding-win32-x64-msvc": "npm:1.8.1"
    napi-postinstall: "npm:^0.2.2"
  dependenciesMeta:
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 10c0/1065ad0d696993cd78a337a84d7093b6da3406236203b579f2ab1348f6ba340f26f7d049863f38970959d05921005b8e4ca34faeba91878a5f7977cc3b3d825b
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.7.11":
  version: 1.11.1
  resolution: "unrs-resolver@npm:1.11.1"
  dependencies:
    "@unrs/resolver-binding-android-arm-eabi": "npm:1.11.1"
    "@unrs/resolver-binding-android-arm64": "npm:1.11.1"
    "@unrs/resolver-binding-darwin-arm64": "npm:1.11.1"
    "@unrs/resolver-binding-darwin-x64": "npm:1.11.1"
    "@unrs/resolver-binding-freebsd-x64": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm-gnueabihf": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm-musleabihf": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-arm64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-linux-ppc64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-riscv64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-riscv64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-linux-s390x-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-x64-gnu": "npm:1.11.1"
    "@unrs/resolver-binding-linux-x64-musl": "npm:1.11.1"
    "@unrs/resolver-binding-wasm32-wasi": "npm:1.11.1"
    "@unrs/resolver-binding-win32-arm64-msvc": "npm:1.11.1"
    "@unrs/resolver-binding-win32-ia32-msvc": "npm:1.11.1"
    "@unrs/resolver-binding-win32-x64-msvc": "npm:1.11.1"
    napi-postinstall: "npm:^0.3.0"
  dependenciesMeta:
    "@unrs/resolver-binding-android-arm-eabi":
      optional: true
    "@unrs/resolver-binding-android-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 10c0/c91b112c71a33d6b24e5c708dab43ab80911f2df8ee65b87cd7a18fb5af446708e98c4b415ca262026ad8df326debcc7ca6a801b2935504d87fd6f0b9d70dce1
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"use-latest-callback@npm:^0.2.3":
  version: 0.2.3
  resolution: "use-latest-callback@npm:0.2.3"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/dc87503f6279ce2980f78e1019231ba20d7509e9d17adac05285babe4d6ba6f68c52f4ef7b5ad777cbc2af9fbaaa09d7adb664ca556da0aebab9f020022880be
  languageName: node
  linkType: hard

"use-latest-callback@npm:^0.2.4":
  version: 0.2.4
  resolution: "use-latest-callback@npm:0.2.4"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/dcdcb4849225638100f662c82ae44eaa7c635ea3a2419f521b45e7f36ede28a49b77f324fa008dccb840acd41a377c0ca8ab36cf4903ee746e96c41c86a9eeb5
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.4.0, use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/2eee5723b0fcce8256f5bfd3112af6c453b5471db00af9c3533e3d5a6e57de83513f9a145a570890457bd7abf2c2aa05797291d950ac666e5a074895dc63168b
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10c0/968bcf1c7c88c04df1ffb463c179558a2ec17aa49e49376120504958239d9e9dad5281aa05f2a78542b8557f2be0b0b4c325710262f3b838b40d703d5ed30c23
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^5.0.0":
  version: 5.0.1
  resolution: "validate-npm-package-name@npm:5.0.1"
  checksum: 10c0/903e738f7387404bb72f7ac34e45d7010c877abd2803dc2d614612527927a40a6d024420033132e667b1bade94544b8a1f65c9431a4eb30d0ce0d80093cd1f74
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 10c0/a8ec5c95d747c840198f20b4973327fa317b98397f341e7a2f352bfcf385aeb73c0eea01cc6d406c20169298375397e259efc317aec53c8ffc001ec998204aed
  languageName: node
  linkType: hard

"walker@npm:^1.0.7, walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10c0/a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"warn-once@npm:0.1.1, warn-once@npm:^0.1.0, warn-once@npm:^0.1.1":
  version: 0.1.1
  resolution: "warn-once@npm:0.1.1"
  checksum: 10c0/f531e7b2382124f51e6d8f97b8c865246db8ab6ff4e53257a2d274e0f02b97d7201eb35db481843dc155815e154ad7afb53b01c4d4db15fb5aa073562496aff7
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^5.0.0":
  version: 5.0.0
  resolution: "webidl-conversions@npm:5.0.0"
  checksum: 10c0/bf31df332ed11e1114bfcae7712d9ab2c37e7faa60ba32d8fdbee785937c0b012eee235c19d2b5d84f5072db84a160e8d08dd382da7f850feec26a4f46add8ff
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: 10c0/fa972dd14091321d38f36a4d062298df58c2248393ef9e8b154493c347c62e2756e25be29c16277396046d6eaa4b11bd174f34e6403fff6aaca9fb30fa1ff46d
  languageName: node
  linkType: hard

"whatwg-url-without-unicode@npm:8.0.0-3":
  version: 8.0.0-3
  resolution: "whatwg-url-without-unicode@npm:8.0.0-3"
  dependencies:
    buffer: "npm:^5.4.3"
    punycode: "npm:^2.1.1"
    webidl-conversions: "npm:^5.0.0"
  checksum: 10c0/c27a637ab7d01981b2e2f576fde2113b9c42247500e093d2f5ba94b515d5c86dbcf70e5cad4b21b8813185f21fa1b4846f53c79fa87995293457e28c889cc0fd
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wonka@npm:^6.3.2":
  version: 6.3.5
  resolution: "wonka@npm:6.3.5"
  checksum: 10c0/044fe5ae26c0a32b0a1603cc0ed71ede8c9febe5bb3adab4fad5e088ceee600a84a08d0deb95a72189bbaf0d510282d183b6fb7b6e9837e7a1c9b209f788dd07
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.7"
  checksum: 10c0/a2c282c95ef5d8e1c27b335ae897b5eca00e85590d92a3fd69a437919b7b93ff36a69ea04145da55829d2164e724bc62202cdb5f4b208b425aba0807889375c7
  languageName: node
  linkType: hard

"write-file-atomic@npm:^5.0.1":
  version: 5.0.1
  resolution: "write-file-atomic@npm:5.0.1"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/e8c850a8e3e74eeadadb8ad23c9d9d63e4e792bd10f4836ed74189ef6e996763959f1249c5650e232f3c77c11169d239cbfc8342fc70f3fe401407d23810505d
  languageName: node
  linkType: hard

"ws@npm:^6.2.3":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
  checksum: 10c0/56a35b9799993cea7ce2260197e7879f21bbbb194a967f31acbbda6f7f46ecda4365951966fb062044c95197e19fb2f053be6f65c172435455186835f494de41
  languageName: node
  linkType: hard

"ws@npm:^7, ws@npm:^7.5.10":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/bd7d5f4aaf04fae7960c23dcb6c6375d525e00f795dd20b9385902bd008c40a94d3db3ce97d878acc7573df852056ca546328b27b39f47609f80fb22a0a9b61d
  languageName: node
  linkType: hard

"ws@npm:^8.12.1":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: "npm:^1.1.0"
    uuid: "npm:^7.0.3"
  checksum: 10c0/51bf35cee52909aeb18f868ecf9828f93b8042fadf968159320f9f11e757a52e43f6563a53b586986cfe5a34d576f3300c4c0cf1e14300084344ae206eaa53c3
  languageName: node
  linkType: hard

"xdate@npm:^0.8.0":
  version: 0.8.3
  resolution: "xdate@npm:0.8.3"
  checksum: 10c0/6a86d7a40f01dd92cd0846169982825ddc73ff6f36213b515685ee80f4b409f140ef4202acba7234f6380f8e2ad30ff9a155fcf349fc8171d0b4ce6480f3319c
  languageName: node
  linkType: hard

"xml2js@npm:0.6.0":
  version: 0.6.0
  resolution: "xml2js@npm:0.6.0"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/db1ad659210eda4b77929aa692271308ec7e04830112161b8c707f3bcc7138947409c8461ae5c8bcb36b378d62594a8d1cb78770ff5c3dc46a68c67a0838b486
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 10c0/665266a8916498ff8d82b3d46d3993913477a254b98149ff7cff060d9b7cc0db7cf5a3dae99aed92355254a808c0e2e3ec74ad1b04aa1061bdb8dfbea26c18b8
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 10c0/f6f7310cf7264a8107e72c1376f4de37389945d2fb4656f8060eca83f01d2d703f9d1b925dd8f39852a57034fafefde6225409ddd9f22aebfda16c6141b71858
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 10c0/0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^16.1.1":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: 10c0/b1dbfefa679848442454b60053a6c95d62f2d2e21dd28def92b647587f415969173c6e99a0f3bab4f1b67ee8283bf735ebe3544013f09491186ba9e8a9a2b651
  languageName: node
  linkType: hard

"yargs@npm:^17.6.2, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zod@npm:^3.25.67":
  version: 3.25.67
  resolution: "zod@npm:3.25.67"
  checksum: 10c0/80a0cab3033272c4ab9312198081f0c4ea88e9673c059aa36dc32024906363729db54bdb78f3dc9d5529bd1601f74974d5a56c0a23e40c6f04a9270c9ff22336
  languageName: node
  linkType: hard
