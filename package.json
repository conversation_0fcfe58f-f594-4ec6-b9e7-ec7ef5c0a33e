{"name": "school-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint ."}, "dependencies": {"@babel/runtime": "^7.27.6", "@expo-google-fonts/inter": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "@rn-primitives/avatar": "^1.2.0", "@rn-primitives/slot": "^1.2.0", "@tabler/icons-react-native": "^3.34.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "expo": "53.0.19", "expo-application": "^6.1.5", "expo-audio": "^0.4.8", "expo-blur": "^14.1.5", "expo-constants": "~17.1.7", "expo-device": "^7.1.4", "expo-font": "^13.3.2", "expo-image": "2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-navigation-bar": "4.2.7", "expo-router": "~5.1.3", "expo-screen-capture": "^7.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-video": "^2.2.2", "lottie-react-native": "7.2.2", "lucide-react-native": "^0.514.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.5", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-crypto-js": "^1.0.0", "react-native-dotenv": "^3.4.11", "react-native-drawer-layout": "^4.1.11", "react-native-gesture-handler": "^2.27.1", "react-native-gifted-charts": "^1.4.61", "react-native-markdown-package": "^1.8.2", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "reactotron-core-client": "^2.9.7", "reactotron-react-native": "^5.1.13", "reactotron-redux": "^3.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^3", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native-crypto-js": "^1", "@types/react-test-renderer": "^19", "babel-jest": "^30.0.4", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "jest": "^30.0.4", "prettier": "^3.5.3", "react-test-renderer": "^19.1.0", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@4.9.2"}