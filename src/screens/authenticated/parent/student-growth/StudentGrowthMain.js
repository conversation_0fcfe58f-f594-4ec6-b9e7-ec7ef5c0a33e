import React, { useEffect } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { theme } from "../../../../styles/theme";
import { transformStudentWithProfilePicture } from "../../../../utils/studentProfileUtils";
import { USER_CATEGORIES } from "../../../../constants/userCategories";
import { setSelectedStudent } from "../../../../state-store/slices/app-slice";

const StudentGrowthMain = () => {
  const dispatch = useDispatch();

  // Get global state
  const { sessionData, selectedStudent } = useSelector((state) => state.app);

  // Get user category from session data
  const userCategory =
    sessionData?.user_category || sessionData?.data?.user_category;
  const isParent = userCategory === USER_CATEGORIES.PARENT;

  // Get student data from backend API response
  const backendStudentList = sessionData?.data?.student_list || [];

  // Transform backend student data to match UI requirements
  const students = backendStudentList.map((student) => {
    return transformStudentWithProfilePicture(student, sessionData);
  });

  // Auto-select first student if none selected and students are available
  useEffect(() => {
    if (students.length > 0 && !selectedStudent) {
      console.log(
        `📈 StudentGrowthMain - Auto-selecting first student: ${students[0]?.student_calling_name}`
      );
      dispatch(setSelectedStudent(students[0]));
    }
  }, [students.length, selectedStudent, dispatch]);

  // Debug logging
  console.log(
    "📈 StudentGrowthMain - User category:",
    userCategory,
    "Is parent:",
    isParent
  );
  console.log("📈 StudentGrowthMain - Students count:", students.length);
  console.log(
    "📈 StudentGrowthMain - Selected student:",
    selectedStudent?.student_calling_name
  );

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <Text style={styles.welcomeText}>Student Growth Dashboard</Text>
        {isParent && selectedStudent && (
          <Text style={styles.studentText}>
            Tracking progress for {selectedStudent.student_calling_name} -{" "}
            {selectedStudent.grade}
          </Text>
        )}
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Simple placeholder content */}
        <View style={styles.placeholderContainer}>
          <MaterialIcons
            name="trending-up"
            size={64}
            color={theme.colors.primary}
          />
          <Text style={styles.placeholderTitle}>Student Growth Dashboard</Text>
          <Text style={styles.placeholderText}>
            This section will display comprehensive student growth metrics,
            charts, and analytics.
          </Text>

          {selectedStudent && (
            <View style={styles.studentInfo}>
              <Text style={styles.studentInfoText}>
                Current Student: {selectedStudent.student_calling_name}
              </Text>
              <Text style={styles.studentInfoText}>
                Grade: {selectedStudent.grade}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8F9FA",
  },
  headerSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  welcomeText: {
    fontFamily: theme.fonts.bold,
    fontSize: 24,
    color: "#000000",
    marginBottom: 4,
  },
  studentText: {
    fontFamily: theme.fonts.regular,
    fontSize: 14,
    color: "#666666",
  },
  scrollContainer: {
    flex: 1,
  },
  placeholderContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    margin: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  placeholderTitle: {
    fontSize: 20,
    fontFamily: theme.fonts.bold,
    color: "#000000",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  placeholderText: {
    fontSize: 16,
    fontFamily: theme.fonts.regular,
    color: "#666666",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 20,
  },
  studentInfo: {
    backgroundColor: "#F8F9FA",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  studentInfoText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: "#333333",
    marginBottom: 4,
  },
  actionButtonsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
});

export default StudentGrowthMain;
