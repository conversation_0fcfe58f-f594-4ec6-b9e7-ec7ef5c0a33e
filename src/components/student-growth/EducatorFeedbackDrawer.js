import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Dimensions,
  Image,
} from 'react-native';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { MaterialIcons } from '@expo/vector-icons';
import { theme } from '../../styles/theme';

const { width } = Dimensions.get('window');

const StarRating = ({ rating, size = 16 }) => {
  const stars = [];
  for (let i = 0; i < 5; i++) {
    stars.push(
      <MaterialIcons
        key={i}
        name={i < rating ? 'star' : 'star-border'}
        size={size}
        color={i < rating ? '#FFD700' : '#E0E0E0'}
      />
    );
  }
  return <View style={styles.starContainer}>{stars}</View>;
};

const FeedbackCard = ({ feedback, onReply }) => {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyText, setReplyText] = useState('');

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const feedbackDate = new Date(timestamp);
    const diffInHours = Math.abs(now - feedbackDate) / 36e5;

    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} Hours ago`;
    } else {
      return feedbackDate.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });
    }
  };

  const handleSendReply = () => {
    if (replyText.trim()) {
      onReply(feedback.id, replyText);
      setReplyText('');
      setShowReplyInput(false);
    }
  };

  return (
    <View style={styles.feedbackCard}>
      <View style={styles.cardContent}>
        {/* Left side - Student Photo and Info */}
        <View style={styles.leftSection}>
          <View style={styles.studentPhoto}>
            <Text style={styles.photoText}>Student Photo</Text>
          </View>
          
          <View style={styles.studentInfo}>
            <Text style={styles.studentName}>Student Calling Name with Title</Text>
            <Text style={styles.admissionNumber}>Admission Number</Text>
            <Text style={styles.gradeLevel}>Grade Level</Text>
          </View>
        </View>

        {/* Right section - Rating and Categories */}
        <View style={styles.rightSection}>
          <View style={styles.ratingRow}>
            <StarRating rating={feedback.rating} size={18} />
            <Text style={styles.ratingText}>{feedback.rating.toFixed(1)}</Text>
          </View>

          <View style={styles.categoriesRow}>
            <View style={styles.primaryCategory}>
              <MaterialIcons name="favorite" size={14} color="#FFFFFF" />
              <Text style={styles.categoryText}>Category 1</Text>
            </View>
            <View style={styles.subCategory}>
              <Text style={styles.subCategoryText}>sub:Category 2</Text>
            </View>
            <View style={styles.subCategory}>
              <Text style={styles.subCategoryText}>sub:Category 3</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Metadata Section */}
      <View style={styles.metadataSection}>
        <Text style={styles.metadataTitle}>Metadata:</Text>
        <Text style={styles.metadataText}>By Educator - {feedback.educatorName}</Text>
        <Text style={styles.metadataText}>By Class Teacher - {feedback.classTeacher}</Text>
        <Text style={styles.metadataText}>By Principal - {feedback.principal}</Text>
        <Text style={styles.metadataText}>Approved By - {feedback.approvedBy}</Text>
        
        <View style={styles.timestampContainer}>
          <Text style={styles.timestampText}>
            Below 24 Hrs = {formatTimestamp(feedback.timestamp)}
          </Text>
          <Text style={styles.timestampText}>
            Else = {new Date(feedback.timestamp).toLocaleDateString('en-US', {
              day: 'numeric',
              month: 'short',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            })}
          </Text>
        </View>
      </View>

      {/* Comment Section */}
      <View style={styles.commentSection}>
        <Text style={styles.commentTitle}>Comment</Text>
        <Text style={styles.commentText}>{feedback.comment}</Text>
      </View>

      {/* Reply Section */}
      <View style={styles.replySection}>
        {!showReplyInput ? (
          <TouchableOpacity
            style={styles.replyButton}
            onPress={() => setShowReplyInput(true)}
          >
            <Text style={styles.replyButtonText}>Reply to Educator</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.replyInputContainer}>
            <View style={styles.replyInputRow}>
              <TextInput
                style={styles.replyInput}
                placeholder="Parent reply message"
                placeholderTextColor="#999999"
                value={replyText}
                onChangeText={setReplyText}
                multiline
              />
              <TouchableOpacity style={styles.sendButton} onPress={handleSendReply}>
                <Text style={styles.sendButtonText}>Send</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const EducatorFeedbackDrawer = ({ isVisible, onClose }) => {
  const bottomSheetRef = useRef(null);
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);

  // Dummy feedback data
  const feedbackData = [
    {
      id: 1,
      educatorName: 'Ms. Sarah Johnson',
      classTeacher: 'Ms. Sarah Johnson',
      principal: 'Mr. Asanka',
      approvedBy: 'Principal Mr. Asanka',
      rating: 3,
      comment: 'Student has shown good progress in mathematics but needs to improve attention during class discussions. Homework completion is consistent.',
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
    },
    {
      id: 2,
      educatorName: 'Mr. David Chen',
      classTeacher: 'Ms. Sarah Johnson',
      principal: 'Mr. Asanka',
      approvedBy: 'Principal Mr. Asanka',
      rating: 4,
      comment: 'Excellent participation in physical education activities. Shows great teamwork and leadership skills during sports.',
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    },
  ];

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      onClose();
    }
  }, [onClose]);

  const handleReply = (feedbackId, replyText) => {
    console.log('Reply to feedback:', feedbackId, replyText);
    // Here you would typically send the reply to your backend
  };

  if (!isVisible) return null;

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose
      backgroundStyle={styles.bottomSheetBackground}
      handleIndicatorStyle={styles.handleIndicator}
    >
      <View style={styles.sheetHeader}>
        <Text style={styles.sheetTitle}>Educator Feedback</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <MaterialIcons name="close" size={24} color="#666666" />
        </TouchableOpacity>
      </View>

      <BottomSheetScrollView style={styles.scrollContainer}>
        {feedbackData.map((feedback) => (
          <FeedbackCard
            key={feedback.id}
            feedback={feedback}
            onReply={handleReply}
          />
        ))}
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomSheetBackground: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handleIndicator: {
    backgroundColor: '#E0E0E0',
    width: 40,
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sheetTitle: {
    fontSize: 20,
    fontFamily: theme.fonts.bold,
    color: '#000000',
  },
  closeButton: {
    padding: 4,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  feedbackCard: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#333333',
  },
  cardContent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  leftSection: {
    flexDirection: 'row',
    flex: 1,
  },
  studentPhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#333333',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#555555',
  },
  photoText: {
    color: '#FFFFFF',
    fontSize: 10,
    textAlign: 'center',
  },
  studentInfo: {
    flex: 1,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#555555',
    padding: 8,
    borderRadius: 8,
  },
  studentName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: theme.fonts.bold,
    marginBottom: 2,
  },
  admissionNumber: {
    color: '#CCCCCC',
    fontSize: 10,
    marginBottom: 2,
  },
  gradeLevel: {
    color: '#CCCCCC',
    fontSize: 10,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  starContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  ratingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: theme.fonts.bold,
  },
  categoriesRow: {
    flexDirection: 'row',
    gap: 6,
  },
  primaryCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#920734',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  categoryText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontFamily: theme.fonts.medium,
  },
  subCategory: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#555555',
  },
  subCategoryText: {
    color: '#CCCCCC',
    fontSize: 10,
  },
  metadataSection: {
    backgroundColor: '#2A2A2A',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#555555',
  },
  metadataTitle: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: theme.fonts.bold,
    marginBottom: 8,
    textAlign: 'center',
  },
  metadataText: {
    color: '#CCCCCC',
    fontSize: 12,
    marginBottom: 2,
    textAlign: 'center',
  },
  timestampContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  timestampText: {
    color: '#999999',
    fontSize: 11,
    textAlign: 'center',
  },
  commentSection: {
    backgroundColor: '#2A2A2A',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#555555',
  },
  commentTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: theme.fonts.bold,
    marginBottom: 8,
    textAlign: 'center',
  },
  commentText: {
    color: '#CCCCCC',
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  replySection: {
    borderTopWidth: 1,
    borderTopColor: '#555555',
    paddingTop: 12,
  },
  replyButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  replyButtonText: {
    color: theme.colors.primary,
    fontSize: 14,
    fontFamily: theme.fonts.medium,
  },
  replyInputContainer: {
    gap: 12,
  },
  replyInputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  replyInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#555555',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#FFFFFF',
    backgroundColor: '#2A2A2A',
    textAlignVertical: 'top',
    minHeight: 60,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: theme.fonts.bold,
  },
});

export default EducatorFeedbackDrawer;
