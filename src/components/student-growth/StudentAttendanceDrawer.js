import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { MaterialIcons } from '@expo/vector-icons';
import { theme } from '../../styles/theme';

const { width } = Dimensions.get('window');

const AttendanceCalendar = ({ attendanceData, selectedMonth, onMonthChange }) => {
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = selectedMonth;
  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = getFirstDayOfMonth(year, month);

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getAttendanceStatus = (day) => {
    const dateKey = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    return attendanceData[dateKey] || 'unknown';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return '#4CAF50';
      case 'absent': return '#F44336';
      case 'weekend': return '#9E9E9E';
      case 'holiday': return '#FF9800';
      default: return '#E0E0E0';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present': return 'check-circle';
      case 'absent': return 'cancel';
      case 'weekend': return 'weekend';
      case 'holiday': return 'celebration';
      default: return 'help';
    }
  };

  const renderCalendarDays = () => {
    const days = [];
    
    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.emptyDay} />);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const status = getAttendanceStatus(day);
      const color = getStatusColor(status);
      const icon = getStatusIcon(status);

      days.push(
        <View key={day} style={styles.dayContainer}>
          <View style={[styles.dayCircle, { backgroundColor: color }]}>
            <MaterialIcons name={icon} size={16} color="#FFFFFF" />
          </View>
          <Text style={styles.dayNumber}>{day}</Text>
        </View>
      );
    }

    return days;
  };

  return (
    <View style={styles.calendarContainer}>
      {/* Month Navigation */}
      <View style={styles.monthHeader}>
        <TouchableOpacity
          onPress={() => onMonthChange(month - 1)}
          style={styles.monthButton}
        >
          <MaterialIcons name="chevron-left" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
        
        <Text style={styles.monthTitle}>
          {monthNames[month]} {year}
        </Text>
        
        <TouchableOpacity
          onPress={() => onMonthChange(month + 1)}
          style={styles.monthButton}
        >
          <MaterialIcons name="chevron-right" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Day Names Header */}
      <View style={styles.dayNamesContainer}>
        {dayNames.map((dayName) => (
          <Text key={dayName} style={styles.dayName}>
            {dayName}
          </Text>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {renderCalendarDays()}
      </View>
    </View>
  );
};

const AttendanceSummary = ({ attendanceData, selectedMonth }) => {
  const calculateStats = () => {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const daysInMonth = new Date(year, selectedMonth + 1, 0).getDate();
    
    let present = 0;
    let absent = 0;
    let weekends = 0;
    let holidays = 0;

    for (let day = 1; day <= daysInMonth; day++) {
      const dateKey = `${year}-${(selectedMonth + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      const status = attendanceData[dateKey] || 'unknown';
      
      switch (status) {
        case 'present': present++; break;
        case 'absent': absent++; break;
        case 'weekend': weekends++; break;
        case 'holiday': holidays++; break;
      }
    }

    const totalSchoolDays = daysInMonth - weekends - holidays;
    const attendanceRate = totalSchoolDays > 0 ? (present / totalSchoolDays * 100).toFixed(1) : 0;

    return { present, absent, weekends, holidays, totalSchoolDays, attendanceRate };
  };

  const stats = calculateStats();

  return (
    <View style={styles.summaryContainer}>
      <Text style={styles.summaryTitle}>Attendance Summary</Text>
      
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#4CAF50' }]}>
            <MaterialIcons name="check-circle" size={20} color="#FFFFFF" />
          </View>
          <Text style={styles.statNumber}>{stats.present}</Text>
          <Text style={styles.statLabel}>Present</Text>
        </View>

        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#F44336' }]}>
            <MaterialIcons name="cancel" size={20} color="#FFFFFF" />
          </View>
          <Text style={styles.statNumber}>{stats.absent}</Text>
          <Text style={styles.statLabel}>Absent</Text>
        </View>

        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#9E9E9E' }]}>
            <MaterialIcons name="weekend" size={20} color="#FFFFFF" />
          </View>
          <Text style={styles.statNumber}>{stats.weekends}</Text>
          <Text style={styles.statLabel}>Weekends</Text>
        </View>

        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#FF9800' }]}>
            <MaterialIcons name="celebration" size={20} color="#FFFFFF" />
          </View>
          <Text style={styles.statNumber}>{stats.holidays}</Text>
          <Text style={styles.statLabel}>Holidays</Text>
        </View>
      </View>

      <View style={styles.attendanceRate}>
        <Text style={styles.rateLabel}>Attendance Rate</Text>
        <Text style={[styles.rateValue, { color: stats.attendanceRate >= 90 ? '#4CAF50' : stats.attendanceRate >= 75 ? '#FF9800' : '#F44336' }]}>
          {stats.attendanceRate}%
        </Text>
      </View>
    </View>
  );
};

const StudentAttendanceDrawer = ({ isVisible, onClose }) => {
  const bottomSheetRef = useRef(null);
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());

  // Dummy attendance data
  const attendanceData = {
    '2024-12-01': 'weekend',
    '2024-12-02': 'present',
    '2024-12-03': 'present',
    '2024-12-04': 'absent',
    '2024-12-05': 'present',
    '2024-12-06': 'present',
    '2024-12-07': 'weekend',
    '2024-12-08': 'weekend',
    '2024-12-09': 'present',
    '2024-12-10': 'present',
    '2024-12-11': 'present',
    '2024-12-12': 'absent',
    '2024-12-13': 'present',
    '2024-12-14': 'weekend',
    '2024-12-15': 'weekend',
    '2024-12-16': 'present',
    '2024-12-17': 'present',
    '2024-12-18': 'present',
    '2024-12-19': 'present',
    '2024-12-20': 'present',
    '2024-12-21': 'weekend',
    '2024-12-22': 'weekend',
    '2024-12-23': 'holiday',
    '2024-12-24': 'holiday',
    '2024-12-25': 'holiday',
    '2024-12-26': 'holiday',
    '2024-12-27': 'holiday',
    '2024-12-28': 'weekend',
    '2024-12-29': 'weekend',
    '2024-12-30': 'present',
    '2024-12-31': 'holiday',
  };

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      onClose();
    }
  }, [onClose]);

  const handleMonthChange = (newMonth) => {
    if (newMonth >= 0 && newMonth <= 11) {
      setSelectedMonth(newMonth);
    }
  };

  if (!isVisible) return null;

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose
      backgroundStyle={styles.bottomSheetBackground}
      handleIndicatorStyle={styles.handleIndicator}
    >
      <View style={styles.sheetHeader}>
        <Text style={styles.sheetTitle}>Student Attendance</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <MaterialIcons name="close" size={24} color="#666666" />
        </TouchableOpacity>
      </View>

      <BottomSheetScrollView style={styles.scrollContainer}>
        <AttendanceCalendar
          attendanceData={attendanceData}
          selectedMonth={selectedMonth}
          onMonthChange={handleMonthChange}
        />
        
        <AttendanceSummary
          attendanceData={attendanceData}
          selectedMonth={selectedMonth}
        />
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomSheetBackground: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handleIndicator: {
    backgroundColor: '#E0E0E0',
    width: 40,
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sheetTitle: {
    fontSize: 20,
    fontFamily: theme.fonts.bold,
    color: '#000000',
  },
  closeButton: {
    padding: 4,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  calendarContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthButton: {
    padding: 8,
  },
  monthTitle: {
    fontSize: 18,
    fontFamily: theme.fonts.bold,
    color: '#000000',
  },
  dayNamesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  dayName: {
    fontSize: 12,
    fontFamily: theme.fonts.medium,
    color: '#666666',
    width: (width - 80) / 7,
    textAlign: 'center',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  emptyDay: {
    width: (width - 80) / 7,
    height: 50,
  },
  dayContainer: {
    width: (width - 80) / 7,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  dayCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 2,
  },
  dayNumber: {
    fontSize: 10,
    fontFamily: theme.fonts.regular,
    color: '#333333',
  },
  summaryContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
  },
  summaryTitle: {
    fontSize: 18,
    fontFamily: theme.fonts.bold,
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 18,
    fontFamily: theme.fonts.bold,
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: theme.fonts.regular,
    color: '#666666',
  },
  attendanceRate: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  rateLabel: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: '#666666',
    marginBottom: 4,
  },
  rateValue: {
    fontSize: 24,
    fontFamily: theme.fonts.bold,
  },
});

export default StudentAttendanceDrawer;
